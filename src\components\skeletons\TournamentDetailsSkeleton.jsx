import React from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

/**
 * Skeleton component for the TournamentDetailsPage
 * Shows loading placeholders while tournament data is being fetched
 */
const TournamentDetailsSkeleton = () => {
  // Define sections to match the actual tournament details view
  const sections = [
    { id: "tournamentDetails", title: "Tournament Details:" },
    { id: "personalInfo", title: "Personnel & Contact Info:" },
    { id: "venueDetails", title: "Tournament Venue Details:" },
    { id: "prizeDetails", title: "Tournament Prize Details:" },
    { id: "miscDetails", title: "Miscellaneous Details:" },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
          backgroundColor: "#f5f9f6",
        }}
      >
        {/* Tournament Header Skeleton */}
        <Box sx={{ p: 3, pb: 0, backgroundColor: "hsla(200, 89%, 89%, 1)" }}>
          <Skeleton
            variant="rectangular"
            width="80%"
            height={40}
            sx={{ mx: "auto", borderRadius: 1 }}
          />
          <Skeleton
            variant="rectangular"
            width="60%"
            height={24}
            sx={{ mx: "auto", mt: 2, borderRadius: 1 }}
          />
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              flexWrap: "wrap",
              justifyContent: "flex-end",
              gap: 2,
              p: 2,
            }}
          >
            <Skeleton variant="text" width={120} />
          </Box>
        </Box>

        {/* Section Accordions */}
        {sections.map((section) => (
          <Accordion
            key={section.id}
            defaultExpanded={true}
            sx={{
              boxShadow: "none",
              "&:before": {
                display: "none",
                margin: "0 !important",
              },
              "&.MuiAccordion-root.Mui-expanded": {
                margin: "0 !important",
              },
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: "#e8f5e9",
                borderTop: "1px solid #c8e6c9",
                borderBottom: "1px solid #c8e6c9",
                minHeight: 56,
                "&.Mui-expanded": {
                  minHeight: 56,
                },
              }}
            >
              <Typography
                sx={{
                  fontWeight: "medium",
                  color: "#2e7d32",
                }}
              >
                {section.title}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              {/* Detail rows skeleton */}
              <Box sx={{ p: 2 }}>
                {Array(5)
                  .fill(0)
                  .map((_, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        borderBottom: "1px solid #e0e0e0",
                        py: 1.5,
                      }}
                    >
                      <Skeleton
                        variant="rectangular"
                        width="40%"
                        height={24}
                        sx={{ borderRadius: 1 }}
                      />
                      <Box sx={{ flexGrow: 1 }} />
                      <Skeleton
                        variant="rectangular"
                        width="50%"
                        height={24}
                        sx={{ borderRadius: 1 }}
                      />
                    </Box>
                  ))}
              </Box>
            </AccordionDetails>
          </Accordion>
        ))}
      </Paper>

      {/* Action Buttons Skeleton */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          flexWrap: "wrap",
          justifyContent: "center",
          gap: 2,
          p: 2,
          borderBottom: "1px solid #f0f0f0",
        }}
      >
        <Skeleton variant="rectangular" width={120} height={36} sx={{ borderRadius: 1 }} />
        <Skeleton variant="rectangular" width={160} height={36} sx={{ borderRadius: 1 }} />
        <Skeleton variant="rectangular" width={140} height={36} sx={{ borderRadius: 1 }} />
      </Box>
    </Container>
  );
};

export default TournamentDetailsSkeleton;
