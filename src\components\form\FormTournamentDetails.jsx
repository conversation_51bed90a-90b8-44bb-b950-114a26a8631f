import React from "react";
import { Typography, Grid, Box, Stack } from "@mui/material";
import FormTextField from "./FormTextField";
import FormRadioField from "./FormRadioField";
import FormSelectField from "./FormSelectField";
import FormDatePicker from "./FormDatePicker";
import FormTimePicker from "./FormTimePicker";
import FormFieldDownText from "./FormFieldDownText";
import FormN<PERSON>ber<PERSON>ield from "./FormNumberField";
import FormInputSelectField from "./FormInputSelectField";

const FormTournamentDetails = ({ control, watch, edit = false, setValue }) => {
  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        Tournament Details:
      </Typography>

      <Box sx={{ mb: 4, mt: 8 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={6}>
            <FormTextField
              name="title"
              control={control}
              title="Tournament Title"
              placeholder="Enter Tournament Title"
              maxLength={250}
              required
              disabled={edit}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormRadioField
              name="fideRated"
              control={control}
              title="FIDE Rated"
              options={[
                { value: true, label: "Yes" },
                { value: false, label: "No" },
              ]}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormTextField
              name="organizerName"
              control={control}
              title="Organizer Name"
              maxLength={50}
              placeholder="Enter Organizer Name"
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormSelectField
              name="tournamentLevel"
              control={control}
              title="Tournament Level"
              placeholder="Select Tournament Level"
              required
              options={[
                { value: "state", label: "State" },
                { value: "national", label: "National" },
                { value: "district", label: "District" },
                { value: "global", label: "Global" },
              ]}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              Tournament Dates<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack direction="row" spacing={2}>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="startDate"
                  title="Start Date"
                  control={control}
                  required
                  minToday={!edit}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="endDate"
                  title="End Date"
                  control={control}
                  required
                  inputProps={{
                    min:
                      watch("startDate") ||
                      (!edit && new Date().toISOString().split("T")[0]),
                  }}
                />
              </Box>
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTimePicker
              name="reportingTime"
              control={control}
              title="Reporting Time"
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              Registration Dates<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack direction="row" spacing={2}>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="registrationStartDate"
                  title="Start Date"
                  control={control}
                  required
                  inputProps={{
                    min: !edit && new Date().toISOString().split("T")[0],
                    max: watch("startDate"),
                  }}
                />
              </Box>
              <Box sx={{ width: "50%" }}>
                <FormDatePicker
                  name="registrationEndDate"
                  title="End Date"
                  control={control}
                  required
                  inputProps={{
                    min:
                      watch("registrationStartDate") ||
                      (!edit && new Date().toISOString().split("T")[0]),
                    max: watch("startDate"),
                  }}
                />
              </Box>
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTimePicker
              name="registrationEndTime"
              control={control}
              title="Registration End Time"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              Registration Fees<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                justifyContent: "center",
                alignItems: "flex-start",
              }}
            >
              <FormSelectField
                name="entryFeeCurrency"
                control={control}
                options={[{ value: "INR", label: "INR" }]}
                sx={{ width: "100px", mt: "4px" }}
              />

              <FormNumberField
                name="entryFee"
                control={control}
                placeholder="Enter Amount"
                minLength={1}
                maxLength={10}
                rules={{
                  required: "Entry fee is required",
                }}
              />
            </Stack>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormSelectField
              name="tournamentCategory"
              control={control}
              title="Tournament Category"
              placeholder="Select Tournament Category"
              required
              options={[
                { value: "open", label: "Open" },
                { value: "male", label: "Male" },
                { value: "female", label: "Female" },
              ]}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormFieldDownText
              name="maleAgeCategory"
              control={control}
              title="Male Age Category"
              required={watch("tournamentCategory") !== "female"}
              setValue={(value) => {
                setValue("maleAgeCategory", value);
              }}
              disabled={watch("tournamentCategory") === "female"}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormFieldDownText
              name="femaleAgeCategory"
              control={control}
              title="Female Age Category"
              setValue={(value) => {
                setValue("femaleAgeCategory", value);
              }}
              required={watch("tournamentCategory") !== "male"}
              disabled={watch("tournamentCategory") === "male"}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12} container spacing={1}>
                <Grid item xs={12} sm={4}>
                  <FormSelectField
                    name="timeControl"
                    control={control}
                    title="Time Control"
                    required
                    options={[
                      { value: "classical", label: "Classical" },
                      { value: "rapid", label: "Rapid" },
                      { value: "bullet", label: "Bullet" },
                      { value: "blitz", label: "Blitz" },
                    ]}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormInputSelectField
                    name="timeControlDuration"
                    control={control}
                    key={"duration"}
                    title="Duration"
                    placeholder="Enter Duration"
                    required
                    max={200}
                    options={[
                      { value: "min", label: "Min" },
                      { value: "sec", label: "Sec" },
                    ]}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormInputSelectField
                    name="timeControlIncrement"
                    key={"increment"}
                    control={control}
                    title="Increment"
                    placeholder="Enter Increment"
                    required
                    max={200}
                    options={[
                      { value: "min", label: "Min" },
                      { value: "sec", label: "Sec" },
                    ]}
                  />
                </Grid>
              </Grid>

              <Grid item xs={12} sx={{ display: "flex", gap: 2 }}>
                <Grid item xs={6}>
                  <FormSelectField
                    name="tournamentSystem"
                    control={control}
                    title="Tournament System"
                    required
                    options={[
                      { value: "swiss", label: "Swiss" },
                      { value: "round-robin", label: "Round-Robin" },
                      { value: "knockout", label: "Knockout" },
                    ]}
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormSelectField
                    name="tournamentType"
                    control={control}
                    required
                    title="Tournament Type"
                    options={[
                      { value: "single", label: "Single" },
                      { value: "team", label: "Team" },
                    ]}
                  />
                </Grid>
              </Grid>
              <Grid item xs={12}>
                <FormNumberField
                  name="numberOfRounds"
                  control={control}
                  title="No. of Rounds"
                  required
                  placeholder="Enter number of rounds"
                  minLength={1}
                  maxLength={5}
                  rules={{
                    required: "Number of rounds is required",
                  }}
                />
              </Grid>
              <Grid item xs={12} mt={3.5}>
                <FormTextField
                  name="chatUrl"
                  control={control}
                  title="Whatsapp group Url"
                  placeholder="Enter Whatsapp group Url"
                />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormTextField
                  name="nationalApproval"
                  control={control}
                  maxLength={50}
                  title="National Approval"
                  placeholder="Enter National Approval No"
                  required={watch("tournamentLevel") === "national"}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="stateApproval"
                  control={control}
                  maxLength={50}
                  title="State Approval"
                  placeholder="Enter State Approval No"
                  required={watch("tournamentLevel") === "state"}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="districtApproval"
                  control={control}
                  maxLength={50}
                  title="District Approval"
                  placeholder="Enter District Approval No"
                  required={watch("tournamentLevel") === "district"}
                />
              </Grid>
              <Grid item xs={12}>
                <FormTextField
                  name="fideApproval"
                  control={control}
                  maxLength={50}
                  title="FIDE Approval"
                  placeholder="Enter FIDE Approval No"
                  required={watch("tournamentLevel") === "global"}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FormTournamentDetails;
