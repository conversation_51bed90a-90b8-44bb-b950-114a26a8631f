import React from "react";
import { Typography, TextField, Box, Grid, Fade } from "@mui/material";
import { Controller } from "react-hook-form";

const FormPhoneInput = React.memo(
  ({
    name,
    control,
    placeholder = "Enter Mobile Number",
    title,
    required = false,
    rules,
    ...rest
  }) => {
    return (
      <Grid item xs={12}>
        {title && (
          <Typography
            variant="h6"
            sx={{
              textAlign: "start",
              p: "0px !important",
              m: "0px !important",
            }}
          >
            {title}
            {required && <span style={{ color: "red" }}>*</span>}
          </Typography>
        )}

        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ field, fieldState: { error } }) => (
            <>
              <Box sx={{ position: "relative", width: "100%",display:'flex' }}>
                <TextField
                  disabled
                  value="+91"
                  size="small"
                  variant="outlined"
                  sx={{
                    width: "75px",
                    mr: 1,
                    bgcolor: "white",
                    borderRadius: 1,
                    padding:"0px !important",
                    "& .MuiInputBase-input": {
                      fontWeight: "bold",
                      textAlign: "center",
                       padding: "8px 14px"
                    },
                  }}
                  InputProps={{
                    readOnly: true,
                  }}
                />

                <TextField
                  {...field}
                  type="tel"
                  value={
                    field.value?.startsWith("91")
                      ? field.value.substring(2)
                      : field.value || ""
                  }
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/[^0-9]/g, "");
                    // Always prefix with 91
                    field.onChange(`91${value}`);
                  }}
                  fullWidth
                  size="small"
                  variant="outlined"
                  placeholder={placeholder + (required ? "*" : "")}
                  inputProps={{
                    maxLength: 10,
                    inputMode: "tel",
                    style: { paddingLeft: "40px" },
                    onKeyDown: (event) => {
                      const allowedKeys = [
                        "Backspace",
                        "Enter",
                        "Delete",
                        "ArrowLeft",
                        "ArrowRight",
                        "Tab",
                        "Home",
                        "End",
                      ];
                      if (
                        !/[0-9]/.test(event.key) &&
                        !allowedKeys.includes(event.key)
                      ) {
                        event.preventDefault();
                      }
                    },
                  }}
                  sx={{
                    bgcolor: "white",
                    borderRadius: 1,
                    // minHeight: 70,
                    "& .MuiInputBase-input": { padding: "8px 14px" },
                    ...rest?.sx,
                  }}
                  error={!!error}
                  {...rest}
                />
              </Box>

              {error && (
                <Fade in={!!error}>
                  <Typography
                    component="span"
                    variant="caption"
                    color="error"
                    sx={{ mt: 0.5, ml: 0.5, display: "block" }}
                  >
                    {error?.message}
                  </Typography>
                </Fade>
              )}
            </>
          )}
        />
      </Grid>
    );
  }
);

export default FormPhoneInput;
