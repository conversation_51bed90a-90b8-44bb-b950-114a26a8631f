import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Box,
  Avatar,
  FormControlLabel,
  Checkbox,
  TextField,
  Container,
  Paper,
  IconButton,
} from "@mui/material";
import { zodResolver } from "@hookform/resolvers/zod";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import FormTextField from "../../components/form/FormTextField";
import FormPhoneInput from "../../components/form/FormPhoneInput";
import FormAutocomplete from "../../components/form/FormAutocomplete";
import FormSelectField from "./FormSelectField";
import { Edit } from "@mui/icons-material";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import { playerProfileSchema, playerProfileEditSchema } from "../../schema/zod";
import ProfileImageUpload from "../common/ProfileImageUpload";
import FormNumberField from "./FormNumberField";
import DocumentUpload from "../documents/DocumentUpload";
import UploadComponent from "../UploadComponent";
import ClubAutocomplete from "./ClubAutocomplete";

const PlayerProfileForm = ({ edit }) => {
  // Initialize React Hook Form

  const resolver = edit
    ? zodResolver(playerProfileEditSchema)
    : zodResolver(playerProfileSchema);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver,
    defaultValues: {
      playerTitle: "",
      firstName: "",
      lastName: "",
      otp: "",
      dob: "",
      gender: "male",
      alternateContact: "",
      fideRating: "",
      aicfId: "",
      districtId: "",
      phoneNumber: "",
      club: "",
      clubs:{},
      other_clubs:false,
      state: "",
      city: "",
      pincode: "",
      address: "",
      parentGuardianName: "",
      emergencyContact: "",
      fideId: "",
      stateId: "",
      association: "",
      country: "",
      district: "",
      termsAndConditions: false,
      countryCode: "",
      stateCode: "",
      profileUrl: "http://demo.com/img",
    },
    reValidateMode: "onChange",
  });

  // Watch for changes to country and state codes for dependent dropdowns
  const countryCode = watch("countryCode");
  const stateCode = watch("stateCode");

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [prefetch, setPrefetch] = useState({});
  const [originalProfileData, setOriginalProfileData] = useState({});
  const toast = UseToast();
  const { user } = UseGlobalContext();

  const navigate = useNavigate();

  // OTP logic
  const [originalPhone, setOriginalPhone] = useState(""); // Store original phone
  const [otpSent, setOtpSent] = useState(false);
  const [otpLoading, setOtpLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [open, setOpen] = useState(false);

  const onClose = () => {
    setOpen(false);
  };

  const handleUpload = async (file, fileName) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("document", file);
      formData.append("name", fileName);

      const response = await Client.post("/player/documents/", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        toast.success("Document uploaded successfully");
        // Refresh the document list
      } else {
        toast.error(response.data.message || "Failed to upload document");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again later.");
    } finally {
      setUploading(false);
    }
  };

  // Calculate if player is under 18 based on date of birth
  const dob = watch("dob");
  const isUnder18 = React.useMemo(() => {
    if (!dob) return false;

    const currentDate = new Date();
    const dobDate = new Date(dob);

    // Calculate age accurately
    let age = currentDate.getFullYear() - dobDate.getFullYear();
    const monthDiff = currentDate.getMonth() - dobDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age < 18;
  }, [dob]);

  // Watch phone number for changes
  const phoneNumber = watch("phoneNumber");

  // Check if phone number has changed from original
  const phoneChanged = React.useMemo(() => {
    return (
      edit && originalPhone && phoneNumber && phoneNumber !== originalPhone
    );
  }, [edit, originalPhone, phoneNumber]);

  // const phoneChanged = true;

  // Load saved draft from localStorage on initial render
  useEffect(() => {
    // Check for saved draft in localStorage
    const savedDraft = localStorage.getItem("playerProfileDraft");
    if (savedDraft && !edit) {
      try {
        const draftData = JSON.parse(savedDraft);
        // Set form values from draft
        Object.keys(draftData).forEach((key) => {
          setValue(key, draftData[key]);
        });
        toast.info("Draft loaded successfully");
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    }
  }, [edit]);

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const response = await Client.get("/location/countries");
        if (!response.data.success) {
          console.error("Failed to fetch countries");
          return;
        }
        setCountries(response.data.data);
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };

    fetchCountries();
  }, []);

  console.log("values",getValues())
  // Load states when country changes
  useEffect(() => {
    // Get current country value
    const country = getValues("country");

    if (country) {
      const countryObj = countries.find((c) => c.name === country);
      if (countryObj) {
        const fetchStates = async () => {
          try {
            const response = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              {
                params: { country: countryObj.isoCode },
              }
            );
            if (!response.data.success) {
              toast.error("Failed to load states");
              return;
            }
            setStates(response.data.data);

            // Only reset dependent fields if not in edit mode or if country has changed
            if (!edit || !getValues("state")) {
              setValue("state", "");
              setValue("stateCode", "");
              setValue("district", "");
              setValue("city", "");
            }
          } catch (error) {
            console.error("Error fetching states:", error);
            toast.error("Failed to load states. Please try again.");
          }
        };
        fetchStates();
      }
    } else {
      setStates([]);
      // Always reset when country is cleared
      setValue("state", "");
      setValue("stateCode", "");
      setValue("district", "");
      setValue("city", "");
    }
  }, [countries, setValue, edit, getValues("country")]);

  useEffect(() => {
    if (getValues("country") === "India" && getValues("state")) {
      const countryObj = countries.find((c) => c.name === getValues("country"));
      const stateObj = states.find((s) => s.name === getValues("state"));
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);

          // Only reset district if not in edit mode or if state has changed
          if (!edit || !getValues("district")) {
            setValue("district", "");
          }
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
      // Always reset when state is cleared
      setValue("district", "");
    }
  }, [getValues("state"), countries, states, setValue, edit]);

  // Load cities when state changes
  useEffect(() => {
    // Get current country and state values
    const country = getValues("country");
    const state = getValues("state");
    const countryCode = getValues("countryCode");
    const stateCode = getValues("stateCode");

    if (country && state && countryCode && stateCode) {
      const fetchCities = async () => {
        try {
          const response = await Client.get(
            `/location/cities/${countryCode}/${stateCode}`,
            {
              params: { country: countryCode, state: stateCode },
            }
          );
          if (!response.data.success) {
            toast.error("Failed to load cities");
            return;
          }
          setCities(response.data.data);

          // Only reset city if not in edit mode or if state has changed
          if (!edit || !getValues("city")) {
            setValue("city", "");
          }
        } catch (error) {
          console.error("Error fetching cities:", error);
          toast.error("Failed to load cities. Please try again.");
        }
      };
      fetchCities();
    } else {
      setCities([]);
      // Reset city only if state is cleared
      if (!state) {
        setValue("city", "");
      }
    }
  }, [getValues("state"), setValue, edit]);

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await Client.get("/user");
        const { email, phoneNumber, cbid, name } = data.data;
        setValue("phoneNumber", phoneNumber);
        setOriginalPhone(phoneNumber); // Set original phone
        const [firstName, lastName] = name && name.split(" ");
        setPrefetch({ email, phoneNumber, cbid, firstName, lastName });
        setOriginalProfileData({
          email,
          phoneNumber,
          cbid,
          firstName,
          lastName,
        });
        setValue("firstName", firstName);
        setValue("lastName", lastName);
      } catch (error) {
        console.error("Error fetching user data:", error);
        const errorMessage =
          error.response?.data?.message ||
          "An unexpected error occurred while fetching user data";
        toast.error(errorMessage);
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!edit) return;
      try {
        // setSubmitting(true);
        const response = await Client.get("/player/profile");
        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          return;
        }
        if (!response.data.success) {
          toast.info(response.data.message);
          return;
        }
        const playerDetails = response.data.data;

        // Process player details to handle null values
        const processedDetails = {
          ...playerDetails,
          // Convert null values to empty strings or appropriate defaults
          playerTitle: playerDetails.playerTitle || "",

          dob: playerDetails.dob || "",
          firstName: prefetch.firstName || "",
          lastName: prefetch.lastName || "",
          phoneNumber: prefetch.phoneNumber || "",
          gender: playerDetails.gender || "male",
          alternateContact: playerDetails.alternateContact || "",
          fideRating: playerDetails.fideRating || "",
          aicfId: playerDetails.aicfId || "",
          districtId: playerDetails.districtId || "",
          club: playerDetails.club || "",
          state: playerDetails.state || "",
          city: playerDetails.city || "",
          pincode: playerDetails.pincode || "",
          address: playerDetails.address || "",
          parentGuardianName: playerDetails.parentGuardianName || "",
          emergencyContact: playerDetails.emergencyContact || "",
          fideId: playerDetails.fideId || "",
          stateId: playerDetails.stateId || "",
          association: playerDetails.association || "",
          country: playerDetails.country || "",
          district: playerDetails.district || "",
          termsAndConditions: playerDetails.termsAndConditions || false,
        };

        // Reset with the processed details
        reset(processedDetails);
        setOriginalProfileData((prev) => ({ ...prev, ...processedDetails,clubId:playerDetails.clubId }));

        // Then find and set the country code
        if (playerDetails.country && countries.length > 0) {
          const countryObj = countries.find(
            (c) => c.name === playerDetails.country
          );
          if (countryObj) {
            setValue("countryCode", countryObj.isoCode);

            // Fetch states for this country
            const statesResponse = await Client.get(
              `/location/states/${countryObj.isoCode}`,
              { params: { country: countryObj.isoCode } }
            );

            if (statesResponse.data.success) {
              const statesData = statesResponse.data.data;
              setStates(statesData);

              // Find and set the state code
              if (playerDetails.state) {
                const stateObj = statesData.find(
                  (s) => s.name === playerDetails.state
                );
                if (stateObj) {
                  setValue("stateCode", stateObj.isoCode);

                  // Fetch cities for this state
                  const citiesResponse = await Client.get(
                    `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
                    {
                      params: {
                        country: countryObj.isoCode,
                        state: stateObj.isoCode,
                      },
                    }
                  );

                  if (citiesResponse.data.success) {
                    setCities(citiesResponse.data.data);
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching player profile:", error);
        let errorMessage = "Error fetching player data";
        if (error.response) {
          errorMessage = error.response.data?.message || "Server error";
        } else if (error.request) {
          errorMessage = "No response from server";
        } else {
          errorMessage = error.message;
        }
        toast.error("Failed to fetch player profile: " + errorMessage);
      } finally {
        // setSubmitting(false);
      }
    };

    fetchProfileData();
  }, [prefetch]);

  const handleSendOtp = async () => {
    if (!edit) return; // Only in edit mode
    if (!phoneNumber) {
      toast.error("Please enter a valid phone number");
      return;
    }

    if (phoneNumber === originalPhone) {
      setOtpSent(false);
      setValue("otp", "");
      return;
    }

    // Validate phone number format before sending OTP
    if (!/^91[0-9]{10}$/.test(phoneNumber)) {
      toast.error("Phone number must start with 91 followed by 10 digits");
      return;
    }

    // Send OTP when phone number changes
    setOtpLoading(true);
    try {
      const response = await Client.post("/auth/send-otp", {
        phoneNumber,
        type: "verification",
        userId: user?.userId,
      });

      if (response.data.success) {
        setOtpSent(true);
        toast.success("OTP sent to new phone number");
      } else {
        setOtpSent(false);
        toast.error(response.data.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      setOtpSent(false);
      if (error.response?.status === 429) {
        toast.error("wait 1 minute before sending another OTP");
        return;
      }
      toast.error(
        error.response?.data?.message || "Failed to send OTP. Try again."
      );
    } finally {
      setOtpLoading(false);
    }
  };

  // Helper to scroll to error field and show error toast
  const validateBeforeSubmit = () => {
    if (!isValid) {
      const firstField = Object.keys(errors)[0];
      const errorData = errors[firstField];

      const sectionElement = document.querySelector(`[name="${firstField}"]`);

      if (sectionElement) {
        const scrollOffset = 100;
        const y =
          sectionElement.getBoundingClientRect().top +
          window.pageYOffset -
          scrollOffset;
        window.scrollTo({ top: y, behavior: "smooth" });

        setTimeout(() => {
          if (sectionElement && "focus" in sectionElement) {
            sectionElement.focus();
          }
        }, 500);
      }

      toast.info(` ${firstField} – ${errorData?.message}`);
      return false;
    }
    return true;
  };

  // Form submission handler
  const onSubmit = async (data) => {
    // Save form data to localStorage as backup
    const dataForStorage = { ...data };
    delete dataForStorage.profileImage;
    delete dataForStorage.profileImagePreview; // Don't store preview in localStorage
    localStorage.setItem("playerProfileDraft", JSON.stringify(dataForStorage));

    toast.info(`Processing your profile ${edit ? "update" : "creation"}...`);

    try {
      // For edit mode, only send changed fields
      if (edit) {
        const changedData = {};

        // Check each field and only include ones that have changed from original
        Object.keys(data).forEach((key) => {
          // Skip otp, phoneChanged, and preview fields from comparison
          if (["otp", "phoneChanged", "profileImagePreview"].includes(key))
            return;

          // Special handling for file fields
          if (key === "profileImage") {
            if (data.profileImage && data.profileImage instanceof File) {
              changedData.profileImage = data.profileImage;
            }
            return;
          }

          // Compare with our stored original data
          if (
            JSON.stringify(data[key]) !==
            JSON.stringify(originalProfileData[key])
          ) {
            changedData[key] = data[key];
          }
        });

        // Always include these fields for user identification
        if (prefetch && prefetch.cbid) {
          changedData.cbid = prefetch.cbid;
        }

        // Handle phone number changes specifically
        if (data.phoneNumber !== originalPhone) {
          changedData.phoneNumber = data.phoneNumber;
          changedData.phoneChanged = true;
          changedData.otp = data.otp || "";
        }
        if (
          data.firstName !== originalProfileData.firstName ||
          data.lastName !== originalProfileData.lastName
        ) {
          changedData.name = `${data.firstName} ${data.lastName}`;
        }

        // Only proceed with the API call if we have changes
        if (
          Object.keys(changedData).length === 0 &&
          !changedData.profileImage
        ) {
          toast.info("No changes detected to update.");
          return;
        }

        // Create FormData to handle file upload
        const formData = new FormData();

        // Add all changed fields to FormData
        Object.keys(changedData).forEach((key) => {
          const value = changedData[key];

          if (key === "profileImage" && value instanceof File) {
            formData.append("profileImage", value);
          } else if (typeof value === "object" && value !== null) {
            // Only stringify if it's an object (not null)
            formData.append(key, JSON.stringify(value));
          } else {
            // No quotes around strings, booleans, or numbers
            formData.append(key, String(value));
          }
        });

        // Log what's being sent (but not the actual file contents)
        const response = await Client.put("/player/profile", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
        handleResponse(response);
        return;
      }

      // For create mode, send all data including image
      const formData = new FormData();

      // Add all fields to FormData except the preview
      Object.keys(data).forEach((key) => {
        if (key === "profileImagePreview") return; // Skip preview

        if (key === "profileImage" && data[key] instanceof File) {
          formData.append("profileImage", data[key]);
        } else {
          formData.append(key, data[key]);
        }
      });

      const response = await Client.post("/player/profile", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      handleResponse(response);
      sessionStorage.removeItem("new_user");
    } catch (error) {
      handleError(error);
    } finally {
      // setSubmitting(false);
    }
  };
  // Helper function to handle the API response
  const handleResponse = (response) => {
    if (!response.data.success) {
      if (response.data.zodErrors) {
        // Show the first Zod validation error
        const zodErrors = response.data.zodErrors;
        const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
        toast.error(firstZodError || "Please correct the validation errors");
      } else {
        toast.error(response.data.error || "Unexpected error");
      }
      return;
    }

    toast.success(`Profile ${edit ? "updated" : "created"} successfully`);

    // Clear draft from localStorage after successful submission
    localStorage.removeItem("playerProfileDraft");

    // Reset the form
    reset();

    // Navigate back to profile page
    navigate("/dashboard/profile");
  };

  // Helper function to handle errors
  const handleError = (error) => {
    console.error(`Error ${edit ? "updating" : "creating"} profile:`, error);

    if (error.response?.data?.error?.message === "Incorrect OTP.") {
      toast.error("Incorrect OTP. Please try again.");
    } else if (error.response?.status === 422 && error.response?.data?.data) {
      const validationErrors = error.response.data.data;
      Object.keys(validationErrors).forEach((key) => {
        if (key !== "_errors" && validationErrors[key]._errors) {
          toast.error(
            `Validation failed - ${key}: ${validationErrors[key]._errors.join(
              ", "
            )}`
          );
        }
      });
    } else if (error.response?.data?.error) {
      toast.error(error.response.data.error.message || "An error occurred");
    } else if (error.response?.data?.zodErrors) {
      // Handle Zod validation errors from the server
      const zodErrors = error.response.data.zodErrors;
      const firstZodError = zodErrors[Object.keys(zodErrors)[0]];
      toast.error(firstZodError || "Please correct the validation errors");
    } else {
      const errorMessage =
        error.response?.data?.message ||
        `An unexpected error occurred while ${
          edit ? "updating" : "creating"
        } the profile.`;
      toast.error(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        container
        spacing={3}
        sx={{
          ".MuiFormControl-root": { mt: "4px !important" },
          ".MuiInputBase-input": { py: 1.5 },
          ".MuiAutocomplete-input": { p: "4px !important" },
          ".MuiGrid-root.MuiGrid-item": { pt: "0px !important" },
          position: "relative",
        }}
      >
        {/* CBID and Profile Image Row */}
        <Grid container item xs={12} md={12} sx={{ mb: 2 }}>
          <Grid item xs={6} md={6}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              CBID: {prefetch.cbid || ""}
            </Typography>
            <Grid item xs={12} md={6}>
              <FormSelectField
                name="playerTitle"
                control={control}
                options={[
                  { value: "Untitled", label: "Untitled" },
                  { value: "GM", label: "Grandmaster" },
                  { value: "IM", label: "International Master" },
                  { value: "FM", label: "FIDE Master" },
                  { value: "CM", label: "Candidate Master" },
                  { value: "WGM", label: "Woman Grandmaster" },
                  { value: "WIM", label: "Woman International Master" },
                  { value: "WFM", label: "Woman FIDE Master" },
                  { value: "WCM", label: "Woman Candidate Master" },
                ]}
                title="Player Title"
                placeholder="Select Player Title"
                rules={{ required: "Player Title is required" }}
              />
            </Grid>
          </Grid>
          <Grid item xs={6} display="flex" justifyContent="center" mb={2}>
            <ProfileImageUpload
              control={control}
              setValue={setValue}
              watch={watch}
            />
          </Grid>
        </Grid>

        {/* Basic Details Fields */}
        <Grid item xs={12} md={6} sx={{ display: "flex", gap: 2 }}>
          <Grid item xs={6}>
            <FormTextField
              name="firstName"
              control={control}
              title="First Name"
              maxLength={50}
              placeholder="Enter First Name"
              required
              disabled={!edit}
            />
          </Grid>
          <Grid item xs={6}>
            <FormTextField
              name="lastName"
              control={control}
              maxLength={50}
              title="Last Name"
              placeholder="Enter Last Name"
              required
              disabled={!edit}
            />
          </Grid>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormSelectField
            name="gender"
            control={control}
            options={[
              { value: "male", label: "Male" },
              { value: "female", label: "Female" },
              { value: "other", label: "Other" },
            ]}
            title="Gender"
            placeholder="Select Gender"
            required
            rules={{ required: "Gender is required" }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="dob"
            control={control}
            title="Date of Birth"
            placeholder="YYYY-MM-DD"
            required
            type="date"
            inputProps={{
              min: "1900-01-01",
              max: new Date().toISOString().split("T")[0],
            }}
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <FormPhoneInput
              name="phoneNumber"
              control={control}
              placeholder="Enter Mobile Number"
              title="Phone Number"
              required={true}
              disabled={!edit}
              rules={{
                required: "Phone number is required",
                pattern: {
                  value: /^91[0-9]{10}$/,
                  message: "Please enter a valid 10-digit phone number",
                },
              }}
            />

            {/* Send OTP button - only show in edit mode when phone number changes */}
            {edit && phoneChanged && (
              <Button
                variant="contained"
                color="primary"
                onClick={handleSendOtp}
                disabled={otpLoading}
                sx={{
                  alignSelf: "flex-end",
                  textWrap: "nowrap",
                  height: "40px",
                  fontSize: "12px",
                  bgcolor: "hsla(242, 56%, 36%, 1)",
                  ":hover": { bgcolor: "rgb(37, 34, 110)" },
                }}
              >
                {otpLoading ? "Sending..." : "Send OTP"}
              </Button>
            )}
          </Box>

          {/* OTP input field - only show when OTP has been sent */}
          {edit && phoneChanged && otpSent && (
            <Box sx={{ mt: 2 }}>
              <FormTextField
                name="otp"
                control={control}
                title="Enter OTP"
                placeholder="Enter the OTP sent to your new phone number"
                required={phoneChanged}
                type="text"
                inputProps={{
                  maxLength: 6,
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                rules={{
                  required: phoneChanged
                    ? "OTP is required to verify your new phone number"
                    : false,
                  minLength: {
                    value: 6,
                    message: "OTP must be 6 digits",
                  },
                  maxLength: {
                    value: 6,
                    message: "OTP must be 6 digits",
                  },
                  validate: (value) => {
                    if (phoneChanged && (!value || value.length !== 6)) {
                      return "Please enter a valid 6-digit OTP to verify your new phone number";
                    }
                    return true;
                  },
                }}
              />
            </Box>
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography
            variant="h6"
            sx={{
              textAlign: "start",
              p: "0px !important",
              m: "0px !important",
            }}
          >
            Email ID
          </Typography>

          <TextField
            fullWidth
            variant="outlined"
            margin="normal"
            value={prefetch.email || ""}
            placeholder={"Enter email"}
            disabled
            sx={{ minHeight: 70 }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="fideId"
            control={control}
            title="FIDE ID"
            placeholder="Enter FIDE ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="fideRating"
            control={control}
            title="FIDE Rating"
            placeholder="Enter FIDE Rating"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="aicfId"
            control={control}
            maxLength={20}
            title="AICF ID"
            placeholder="Enter AICF ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="stateId"
            control={control}
            title="State ID"
            maxLength={20}
            placeholder="Enter State ID"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormTextField
            name="districtId"
            control={control}
            title="District ID"
            maxLength={20}
            placeholder="Enter District ID"
          />
        </Grid>

        <Grid item xs={12} md={6}>
           {!originalProfileData.clubId  ? (
              <ClubAutocomplete
                name="clubs"
                control={control}
                title="Club Name"
                 setValue={setValue}
                placeholder="Search for a club name"
                required={true}
                defaultValue={{ name: null, id: null }}
                rules={{
                  required: "Please select a club name",
                }}
              />
            ) : (
              <FormTextField
                name="club"
                maxLength={50}
                control={control}
                value={watch("club")}
                title="Club Name"
                placeholder="Enter Club Name"
                required
                disabled
              />
            )}
        </Grid>
        <Grid item xs={12} md={6}>
          <FormPhoneInput
            name="alternateContact"
            control={control}
            title="Alternate Contact"
            placeholder="Enter Alternate Contact"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="parentGuardianName"
            control={control}
            title="Parent/Guardian Name"
            maxLength={100}
            placeholder="Enter Parent/Guardian Name"
            required={isUnder18}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormPhoneInput
            name="emergencyContact"
            control={control}
            title="Parent/Guardian Contact"
            placeholder="Enter Parent/Guardian Contact"
            required={isUnder18}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="association"
            control={control}
            maxLength={100}
            title="Association"
            placeholder="Enter Association"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="country"
            control={control}
            options={countries}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="Country"
            placeholder="Select Country"
            maxLength={50}
            required
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("country", newValue.name);
                setValue("countryCode", newValue.isoCode);
                setValue("state", "");
                setValue("stateCode", "");
                setValue("city", "");
              }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="state"
            control={control}
            options={states}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            getOptionLabel={(option) => option.name}
            title="State"
            placeholder="Select State"
            required
            disabled={!countryCode}
            onChange={(_, newValue) => {
              if (newValue) {
                setValue("state", newValue.name);
                setValue("stateCode", newValue.isoCode);
                setValue("city", "");
              }
            }}
          />
        </Grid>

        {getValues("country") === "India" && (
          <Grid item xs={12} md={6}>
            <FormAutocomplete
              name="district"
              control={control}
              sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
              title="District"
              placeholder="Select District"
              required
              options={districts}
              getOptionLabel={(option) => option.name}
              disabled={!stateCode || !countryCode}
              onChange={(_, newValue) => {
                if (newValue) {
                  setValue("district", newValue.name);
                }
              }}
            />
          </Grid>
        )}
        {(getValues("country") !== "India" || getValues("country") === "") && (
          <Grid item xs={12} md={6}>
            <FormTextField
              name="district"
              control={control}
              title="District"
              placeholder="Enter District"
              required
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <FormAutocomplete
            name="city"
            control={control}
            sx={{ "& .MuiInputBase-root": { p: "4px 10px !important " } }}
            options={cities}
            getOptionLabel={(option) => option.name}
            title="City"
            placeholder="Select City"
            required
            disabled={!stateCode || !countryCode}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="pincode"
            control={control}
            title="Pincode"
            placeholder="Enter Pincode"
            required
            inputProps={{
              inputMode: "numeric",
              pattern: "[0-9]*",
              maxLength: 6,
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="address"
            control={control}
            title="Address"
            placeholder="Enter Address"
            maxLength={150}
            required
            multiline
            rows={3}
          />
        </Grid>
        {sessionStorage.getItem("new_user") === "true" && (
          <Grid
            item
            xs={12}
            md={6}
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              variant="contained"
              sx={{ bgcolor: "hsla(242, 56%, 36%, 1) ", fontSize: "16px" }}
              onClick={() => setOpen(true)}
            >
              Upload Documents
            </Button>
            <DocumentUpload
              open={open}
              onClose={onClose}
              onUpload={handleUpload}
              isUploading={uploading}
            />
          </Grid>
        )}

        {/* Terms and Conditions */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ textAlign: "start" }}>
            Declaration Statement
          </Typography>
          <Typography
            style={{ fontSize: 14 }}
            variant="h6"
            sx={{ textAlign: "start", fontSize: 14 }}
          >
            I will be solely responsible for my involvement in any kind of
            unlawful activities
          </Typography>
          <Controller
            name="termsAndConditions"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={field.value}
                    sx={{
                      color: "hsla(0, 3%, 80%, 1)",
                      "&.Mui-checked": {
                        color: "black",
                      },
                      "&.MuiCheckbox-indeterminate": {
                        color: "hsla(0, 3%, 80%, 1)",
                      },
                    }}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                }
                label="I agree to the terms and conditions"
              />
            )}
          />
          {errors && errors.termsAndConditions && (
            <Typography
              variant="caption"
              color="error"
              sx={{ ml: 1.5, mt: 0.5 }}
            >
              {"Please agree to the terms and conditions"}
            </Typography>
          )}
        </Grid>

        {/* Submit Buttons */}
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 2,
              mt: 6,
            }}
          >
            <Button
              type="submit"
              variant="contained"
              sx={{
                fontSize: 16,
                px: 2,
                bgcolor: isValid
                  ? "hsla(120, 49%, 35%, 1)"
                  : "hsla(0, 3%, 80%, 1)",
                "&:hover": {
                  bgcolor: isValid ? "rgb(39, 104, 39)" : "hsla(0, 3%, 80%, 1)",
                },
              }}
              size="large"
              onClick={() => validateBeforeSubmit()}
            >
              {edit ? "Update" : "Create"}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </form>
  );
};

export default PlayerProfileForm;
