import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  <PERSON>ert,
  <PERSON>per,
  Step,
  StepLabel,
  Select,
  MenuItem,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useMemo } from "react";
import CloseIcon from "@mui/icons-material/Close";
import PaymentIcon from "@mui/icons-material/Payment";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import getEligibleCategories from "../../utils/age";

const PaymentModal = ({
  open,

  onClose,
  setLoading,
  tournament,
}) => {
  const [error, setError] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const { user } = UseGlobalContext();
  const [userDetails, setUserDetails] = useState([]);
  const [eligible, setEligible] = useState("");
  const [eligibleError, setEligibleError] = useState(false);
  const toast = UseToast();
  const category = tournament?.tournamentCategory;
  const [gender, setGender] = useState(category !== "open" ? category : "");
  const [ageGroup, setAgeGroup] = useState("");
  // Define steps in the registration process
  const steps = ["Eligibility Check", "Payment"];

  const eligibleCategories = useMemo(
    () =>
      getEligibleCategories(
        userDetails["dob"],
        gender,
        tournament.maleAgeCategory,
        tournament?.femaleAgeCategory
      ),
    [userDetails, gender]
  );

  useEffect(() => {
    if (eligibleCategories === false) {
      setEligible(
        "You are not eligible to register for any age group in this tournament."
      );
      setEligibleError(true);
    } else {
      setEligible("");
      setEligibleError(false);
    }
  }, [eligibleCategories]);

  const handleClose = () => {
    setActiveStep(0);
    onClose();
  };
  const initiatePayment = async (tournamentId) => {
    // setLoading(true);
    try {
      // Call the payment initiation endpoint with tournament registration action
      const response = await Client.post("/payment/initiate", {
        tournamentId,
        action: "tournament_registration",
        ageCategory: ageGroup,
        genderCategory: gender,
        // Include any additional data needed for registration in the webhook
        registrationType: "player",
        registerAfterPayment: true, // Flag to indicate registration should happen after payment
      });

      if (!response.data.success) {
        toast.error(response.data.message || "Payment initiation failed");
        return;
      }

      const data = response.data.data;

      const form = document.createElement("form");
      form.method = "post";
      form.action = data.paymentUrl;

      Object.entries(data.formData).forEach(([key, value]) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
    } catch (error) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("An error occurred while initiating payment");
        console.error("Payment initiation error:", error);
      }
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (category === "open" && userDetails["gender"] === "male") {
      setGender("male");
    } else if (category === "male" || category === "female") {
      setGender(category);
    }
  }, [category, userDetails]);

  useEffect(() => {
    getUser();
  }, []);

  const handleNext = () => {
    if (!gender) {
      toast.error("Please select your gender category");
      return;
    }

    // Check 2: Tournament gender validation (strict male/female tournaments)
    if (
      tournament.tournamentCategory === "male" &&
      userDetails["gender"] !== "male"
    ) {
      toast.error("Only male players can register for this tournament.");
      return;
    }

    if (
      (tournament.tournamentCategory === "male" &&
        userDetails.gender !== "male") ||
      (tournament.tournamentCategory === "female" &&
        userDetails.gender !== "female")
    ) {
      toast.error(
        `Only ${tournament.tournamentCategory} players can register for this tournament.`
      );
      return;
    }

    if (ageGroup === "") {
      toast.error("Please select an age group");
      return;
    }

    setActiveStep((prevStep) => prevStep + 1);
  };

  const getUser = async () => {
    try {
      // Call the eligibility check endpoint
      const response = await Client.get(`/player/profile`);

      if (response.data.success) {
        setUserDetails(response.data.data);
        if (
          (tournament.tournamentCategory === "male" &&
            response.data.data.gender !== "male") ||
          (tournament.tournamentCategory === "female" &&
            response.data.data.gender !== "female")
        ) {
          setEligible(
            `Only ${tournament.tournamentCategory} players can register for this tournament.`
          );
          setEligibleError(true);
        }
      }
    } catch (error) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      }
    }
  };

  // // Handle form submission when user clicks proceed to payment
  const handleProceedToPayment = async () => {
    if (activeStep === 1) {
      // setRedirecting(true);
      try {
        await initiatePayment(tournament.id);
      } catch (error) {
        console.error("Payment form submission error:", error);
        setError("Failed to redirect to payment gateway. Please try again.");
      }
    }
  };

  const menuItems =
    category === "open"
      ? userDetails.gender === "male"
        ? [
            <MenuItem key="male" value="male">
              Male
            </MenuItem>,
          ]
        : [
            <MenuItem key="male" value="male">
              Male
            </MenuItem>,
            <MenuItem key="female" value="female">
              Female
            </MenuItem>,
          ]
      : [
          <MenuItem key={category} value={category}>
            {category}
          </MenuItem>,
        ];

  if (activeStep === 0) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h5">Tournament Registration</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Stepper activeStep={0} alternativeLabel sx={{ mb: 4, mt: 2 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>
                  <Typography variant="h6" sx={{ fontSize: 16 }}>
                    {label}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
          {eligibleError ? (
            <Typography color="error">{eligible}</Typography>
          ) : (
            <>
              {/* Select Fields */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" fontWeight="bold" mb={1}>
                  Choose Your Category:
                </Typography>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  <Box flex={1}>
                    <Typography
                      maxWidth={"fit-content"}
                      sx={{ fontWeight: "600", fontSize: "16px" }}
                      mb={0.5}
                    >
                      Category
                    </Typography>
                    <Select
                      fullWidth
                      value={gender}
                      disabled={category !== "open"}
                      size="small"
                      displayEmpty
                      onChange={(e) => setGender(e.target.value)}
                    >
                      <MenuItem value="">Select Category</MenuItem>
                      {menuItems}
                    </Select>
                  </Box>
                  <Box flex={1}>
                    <Typography
                      maxWidth={"fit-content"}
                      sx={{ fontWeight: "600", fontSize: "16px" }}
                      mb={0.5}
                    >
                      Age Group
                    </Typography>
                    <Select
                      fullWidth
                      value={ageGroup}
                      size="small"
                      displayEmpty
                      onChange={(e) => setAgeGroup(e.target.value)}
                    >
                      <MenuItem value="" disabled>
                        Select Age Group
                      </MenuItem>
                      {eligibleCategories !== false &&
                        eligibleCategories.map((category, index) => (
                          <MenuItem key={index} value={category}>
                            {category}
                          </MenuItem>
                        ))}
                    </Select>
                  </Box>
                </Box>
              </Box>

              {/* Eligibility Check Loader */}

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  pt: 4,
                  gap: 2,
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ fontSize: 16 }}
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ fontSize: 16 }}
                  onClick={() => handleNext()}
                >
                  Next
                </Button>
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h5">Tournament Registration</Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {/* Stepper to show registration progress */}
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4, mt: 2 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>
                <Typography variant="h6" sx={{ fontSize: 16 }}>
                  {label}
                </Typography>
              </StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <CheckCircleIcon color="success" sx={{ mr: 1 }} />
            <Typography variant="h6">Eligibility Check Successful!</Typography>
          </Box>
          <Typography variant="body1">
            You are eligible to participate in this tournament. Please proceed
            to payment to complete your registration.
          </Typography>
          <Alert severity="info" sx={{ mt: 2, fontSize: 16 }}>
            Important: Your registration will only be confirmed after successful
            payment. The system will automatically register you once payment is
            complete.
          </Alert>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Payment Details:
          </Typography>
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
            <Typography variant="body1">Amount:</Typography>
            <Typography variant="body1" fontWeight="medium">
              ₹{tournament.entryFee}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
            <Typography variant="body1">Player Name:</Typography>
            <Typography variant="body1" fontWeight="medium">
              {user.name}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
            <Typography variant="body1">Tournament:</Typography>
            <Typography variant="body1" fontWeight="medium">
              {tournament.title}
            </Typography>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="primary" sx={{ fontSize: 16 }}>
          Cancel
        </Button>

        <Button
          onClick={handleProceedToPayment}
          variant="contained"
          color="primary"
          startIcon={<PaymentIcon />}
          sx={{ fontSize: 16 }}
        >
          Proceed to Payment
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentModal;
