import React, { useState, useEffect } from "react";
import { <PERSON>, Button, Container } from "@mui/material";
import { Client } from "../../api/client";

import { Link } from "react-router-dom";
import DynamicTable from "../../components/common/DynamicTable";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { ArrowBack } from "@mui/icons-material";
import ArbiterSearchForm from "../../components/form/ArbiterSearchForm";
import BackButton from "../../components/common/BackButton";

const ArbitersPage = () => {
  // States for form inputs
  const [search, setSearch] = useState({
    arbiterName: "",
    arbiterId: "",
    country: "",
    state: "",
    district: "",
    city: "",
  });

  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [arbiters, setArbiters] = useState([]);

  const toast = UseToast();

  // Define fetchArbiters function without useCallback to avoid dependency issues
  const fetchArbiters = async () => {
    setLoading(true);

    try {
      const response = await Client.get("/arbiter", {
        params: { ...search, page, limit },
      });
      if (response.status === 204) {
        setArbiters([]);
        toast.info("No arbiters found");
        return;
      }
      const { arbiters, currentPage: cp, totalPages: tp } = response.data.data;
      setTotalPages(tp || 1);
      setPage(cp || 1);
      setArbiters(arbiters);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching arbiters:", error);
        toast.error("Failed to fetch arbiters. Please try again.");
      }
      setArbiters([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = (newPage) => {
    setPage(newPage);
    fetchArbiters(newPage);
  };
  const handleReset = () => {
    setSearch({
      arbiterName: "",
      arbiterId: "",
      country: "",
      state: "",
      district: "",
      city: "",
    });
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchArbiters(newPage);
  };

  return (
    <Container maxWidth="xl" sx={{ pt: 2, pb: 8 }}>
      {/* Search Form */}
      <BackButton />
      <ArbiterSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        handleReset={handleReset}
      />

      {/* Arbiters Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          { id: "title", label: "Title", width: "80px" },
          {
            id: "name",
            label: "Arbiter Name",
            format: (_, item) => (
              <Link to={`/arbiters/${item.cbid}`}>{item.name}</Link>
            ),
          },
          { id: "fideId", label: "FIDE ID" },
          { id: "aicfId", label: "AICF ID" },
          { id: "stateId", label: "State ID" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
        ]}
        data={arbiters}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/arbiters/"
        idField="cbid"
  
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default ArbitersPage;
