import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  Chip,
  Divider,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import TemplateSelector from "../../components/admin/TemplateSelector";
import MessagePreview from "../../components/admin/MessagePreview";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const SMSComposer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();
  
  const { selectedUsers = [], selectedType = "", mode = "sms" } = location.state || {};
  
  const [smsData, setSmsData] = useState({
    content: "",
    templateId: "",
    useTemplate: false,
  });
  
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (selectedUsers.length === 0) {
      toast.error("No recipients selected");
      navigate("/admin/dashboard/sms");
      return;
    }
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/admin/sms-templates");
      if (response.data.success) {
        setTemplates(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error("Failed to load SMS templates");
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    if (template) {
      setSmsData(prev => ({
        ...prev,
        content: template.content || "",
        templateId: template.id,
        useTemplate: true,
      }));
    } else {
      setSmsData(prev => ({
        ...prev,
        content: "",
        templateId: "",
        useTemplate: false,
      }));
    }
  };

  const handleContentChange = (value) => {
    setSmsData(prev => ({
      ...prev,
      content: value,
      useTemplate: false, // Disable template when manually editing
    }));
  };

  const validateSMS = () => {
    if (!smsData.content.trim()) {
      toast.error("SMS content is required");
      return false;
    }
    if (smsData.content.length > 1600) {
      toast.error("SMS content is too long (maximum 1600 characters)");
      return false;
    }
    return true;
  };

  const handleSendSMS = async () => {
    if (!validateSMS()) return;

    setSending(true);
    try {
      const payload = {
        recipients: selectedUsers.map(user => ({
          id: user.id,
          phone: user.phone,
          name: user.name,
          type: user.type,
        })),
        content: smsData.content,
        templateId: smsData.useTemplate ? smsData.templateId : null,
        recipientType: selectedType,
      };

      const response = await Client.post("/admin/send-sms", payload);
      
      if (response.data.success) {
        toast.success(`SMS sent successfully to ${selectedUsers.length} recipients`);
        navigate("/admin/dashboard/sms");
      } else {
        toast.error(response.data.message || "Failed to send SMS");
      }
    } catch (error) {
      console.error("Error sending SMS:", error);
      toast.error("Failed to send SMS. Please try again.");
    } finally {
      setSending(false);
    }
  };

  const getRecipientSummary = () => {
    const typeLabel = selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
    return `${selectedUsers.length} ${typeLabel}${selectedUsers.length > 1 ? 's' : ''}`;
  };

  const getCharacterCount = () => {
    return smsData.content.length;
  };

  const getSMSCount = () => {
    return Math.ceil(smsData.content.length / 160);
  };

  const getRecipientsWithPhone = () => {
    return selectedUsers.filter(user => user.phone);
  };

  const getRecipientsWithoutPhone = () => {
    return selectedUsers.filter(user => !user.phone);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Compose SMS
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Send SMS to selected recipients
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Composition Area */}
        <Grid item xs={12} md={8}>
          {/* Recipients Summary */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recipients ({getRecipientSummary()})
            </Typography>
            
            {/* Recipients with phone numbers */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Recipients with phone numbers ({getRecipientsWithPhone().length}):
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {getRecipientsWithPhone().slice(0, 10).map((user, index) => (
                  <Chip
                    key={index}
                    label={`${user.name} (${user.phone})`}
                    variant="outlined"
                    size="small"
                    color="success"
                  />
                ))}
                {getRecipientsWithPhone().length > 10 && (
                  <Chip
                    label={`+${getRecipientsWithPhone().length - 10} more`}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                )}
              </Box>
            </Box>

            {/* Recipients without phone numbers */}
            {getRecipientsWithoutPhone().length > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  {getRecipientsWithoutPhone().length} recipient(s) don't have phone numbers and will be skipped:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                  {getRecipientsWithoutPhone().slice(0, 5).map((user, index) => (
                    <Chip
                      key={index}
                      label={user.name}
                      variant="outlined"
                      size="small"
                      color="warning"
                    />
                  ))}
                  {getRecipientsWithoutPhone().length > 5 && (
                    <Chip
                      label={`+${getRecipientsWithoutPhone().length - 5} more`}
                      variant="outlined"
                      size="small"
                      color="warning"
                    />
                  )}
                </Box>
              </Alert>
            )}
          </Paper>

          {/* Template Selection */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              SMS Template (Required)
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              For SMS, you must select from predefined templates only.
            </Typography>
            <TemplateSelector
              templates={templates}
              selectedTemplate={smsData.templateId}
              onTemplateSelect={handleTemplateSelect}
              loading={loading}
              type="sms"
            />
          </Paper>

          {/* SMS Content */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              SMS Content
            </Typography>
            
            <TextField
              label="SMS Message"
              fullWidth
              multiline
              rows={6}
              value={smsData.content}
              onChange={(e) => handleContentChange(e.target.value)}
              placeholder="Select a template above or enter your SMS content..."
              helperText={`${getCharacterCount()}/1600 characters (${getSMSCount()} SMS${getSMSCount() > 1 ? ' messages' : ''})`}
              error={getCharacterCount() > 1600}
              disabled={!smsData.useTemplate} // Only allow editing if not using template
              sx={{ mb: 2 }}
            />

            {getSMSCount() > 1 && (
              <Alert severity="info">
                This message will be sent as {getSMSCount()} SMS messages due to length.
              </Alert>
            )}

            {getCharacterCount() > 1600 && (
              <Alert severity="error" sx={{ mt: 1 }}>
                Message is too long. Please reduce the content to 1600 characters or less.
              </Alert>
            )}
          </Paper>

          {/* Action Buttons */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                onClick={() => setShowPreview(true)}
                disabled={!smsData.content}
              >
                Preview
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSendSMS}
                disabled={sending || !smsData.content || getRecipientsWithPhone().length === 0}
                startIcon={sending && <CircularProgress size={20} />}
              >
                {sending ? "Sending..." : `Send SMS to ${getRecipientsWithPhone().length} recipients`}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              SMS Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total Recipients
              </Typography>
              <Typography variant="body1">
                {selectedUsers.length}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Valid Phone Numbers
              </Typography>
              <Typography variant="body1" color={getRecipientsWithPhone().length === 0 ? "error" : "inherit"}>
                {getRecipientsWithPhone().length}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Template
              </Typography>
              <Typography variant="body1">
                {smsData.useTemplate ? "Using template" : "No template selected"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Character Count
              </Typography>
              <Typography variant="body1" color={getCharacterCount() > 1600 ? "error" : "inherit"}>
                {getCharacterCount()}/1600
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                SMS Count
              </Typography>
              <Typography variant="body1">
                {getSMSCount()} message{getSMSCount() > 1 ? 's' : ''}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Preview Modal */}
      <MessagePreview
        open={showPreview}
        onClose={() => setShowPreview(false)}
        content={smsData.content}
        type="sms"
        recipients={getRecipientsWithPhone()}
      />
    </Container>
  );
};

export default SMSComposer;
