import React, { useState, useEffect } from "react";
import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import {
  ArrowCircleDownRounded,
  RestartAlt,
  Search as SearchIcon,
} from "@mui/icons-material";

import { Client } from "../../api/client";
import useUserGeoInfo from "../../lib/hooks/UseGetlocation";

const TournamentSearchForm = ({
  search,
  setSearch,
  handleSearch,
  loading,
  handleReset,
}) => {
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [locationLoaded, setLocationLoaded] = useState(false);
  const { geoInfo } = useUserGeoInfo();

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      const response = await Client.get("/location/countries");
      if (!response.data.success) return;
      setCountries(response.data.data);
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (search.country) {
      const countryObj = countries.find((c) => c.name === search.country);
      if (countryObj) {
        const fetchStates = async () => {
          const response = await Client.get(
            `/location/states/${countryObj.isoCode}`,
            {
              params: { country: countryObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setStates(response.data.data);
        };
        fetchStates();
      }
    } else {
      setStates([]);
    }
    // Reset dependent fields
    setSearch((prev) => ({ ...prev, state: "", district: "", city: "" }));
  }, [search.country, countries]);

  // Load districts when state changes
  useEffect(() => {
    if (search.country === "India" && search.country && search.state) {
      const countryObj = countries.find((c) => c.name === search.country);
      const stateObj = states.find((s) => s.name === search.state);
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
    }
    // Reset dependent field
    setSearch((prev) => ({ ...prev, city: "", district: "" }));
  }, [search.country, search.state, countries, states]);

  useEffect(() => {
    if (!geoInfo || countries.length === 0) return;

    const countryObj = countries.find((c) => c.isoCode === geoInfo.country);
    if (countryObj) {
      setSearch((prev) => ({ ...prev, country: countryObj.name }));

      const loadStatesAndSet = async () => {
        const stateRes = await Client.get(
          `/location/states/${countryObj.isoCode}`
        );
        if (!stateRes.data.success) return;

        setStates(stateRes.data.data);
        const stateObj = stateRes.data.data.find(
          (s) => s.name === geoInfo.state
        );

        if (stateObj) {
          setSearch((prev) => ({ ...prev, state: stateObj.name }));

          // Load districts if applicable
          if (countryObj.name === "India") {
            const distRes = await Client.get(
              `/location/districts/${stateObj.isoCode}`,
              {
                params: {
                  country: countryObj.isoCode,
                  state: stateObj.isoCode,
                },
              }
            );
            if (distRes.data.success) {
              setDistricts(distRes.data.data);
            }
          }

          // Load cities
          const cityRes = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (cityRes.data.success) {
            setCities(cityRes.data.data);
            const cityObj = cityRes.data.data.find(
              (c) => c.name === geoInfo.city
            );
            if (cityObj) {
              setSearch((prev) => ({ ...prev, city: cityObj.name }));
            }
          }
        }
        setLocationLoaded(true);
      };

      loadStatesAndSet();
    }
  }, [geoInfo, countries]);

  useEffect(() => {
    if (locationLoaded) {
      handleSearch(1);
      // Reset flag to prevent repeated searches
      setLocationLoaded(false);
    }
  }, [locationLoaded, handleSearch]);

  // Load cities when state changes
  useEffect(() => {
    if (search.country && search.state) {
      const countryObj = countries.find((c) => c.name === search.country);
      const stateObj = states.find((s) => s.name === search.state);
      if (countryObj && stateObj) {
        const fetchCities = async () => {
          const response = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setCities(response.data.data);
        };
        fetchCities();
      }
    } else {
      setCities([]);
      setSearch((prev) => ({ ...prev, district: "" }));
    }
    // Reset dependent field
    setSearch((prev) => ({ ...prev, city: "" }));
  }, [search.country, search.state, countries, states]);
  // Handle input changes without triggering search
  const handleInputChange = (field, value) => {
    setSearch((prev) => ({ ...prev, [field]: value }));
  };

  // Handle key press in input fields (for Enter key)
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch(1);
    }
  };

  const monthValues = [
    { month: "Jan", value: 1 },
    { month: "Feb", value: 2 },
    { month: "Mar", value: 3 },
    { month: "Apr", value: 4 },
    { month: "May", value: 5 },
    { month: "Jun", value: 6 },
    { month: "Jul", value: 7 },
    { month: "Aug", value: 8 },
    { month: "Sep", value: 9 },
    { month: "Oct", value: 10 },
    { month: "Nov", value: 11 },
    { month: "Dec", value: 12 },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  return (
    <Paper sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9" }}>
      <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
        <Typography variant="h5" sx={{ color: "#3f51b5" }}>
          Search Tournaments
        </Typography>
      </Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Tournament Title"
            variant="outlined"
            name="title"
            fullWidth
            size="small"
            value={search.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.tournamentType || ""}
            onChange={(e) =>
              handleInputChange("tournamentType", e.target.value)
            }
            renderValue={(selected) =>
              selected ? selected : "Tournament Type"
            }
            sx={{ bgcolor: "white", textTransform: "capitalize" }}
          >
            <MenuItem value="">
               All 
            </MenuItem>
            <MenuItem value="rated">Rated</MenuItem>
            <MenuItem value="unrated">Unrated</MenuItem>
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.tournamentCategory || ""}
            onChange={(e) =>
              handleInputChange("tournamentCategory", e.target.value)
            }
            renderValue={(selected) =>
              selected ? selected : "Gender Category"
            }
            sx={{ bgcolor: "white", textTransform: "capitalize" }}
          >
            <MenuItem value="open">
               Open 
            </MenuItem>
            <MenuItem value="male">Male</MenuItem>
            <MenuItem value="female">Female</MenuItem>
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Age"
            variant="outlined"
            name="age"
            fullWidth
            size="small"
            type="number"
            value={search.age}
            onChange={(e) => handleInputChange("age", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
            inputProps={{
              min: 0,
              max: 200,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.country}
            onChange={(e) => handleInputChange("country", e.target.value)}
            renderValue={(selected) => (selected ? selected : "Country")}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
               Country 
            </MenuItem>
            {countries.map((country) => (
              <MenuItem key={country.isoCode} value={country.name}>
                {country.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.state}
            onChange={(e) => handleInputChange("state", e.target.value)}
            renderValue={(selected) => (selected ? selected : "State")}
            sx={{ bgcolor: "white" }}
            disabled={!search.country}
          >
            <MenuItem value="">
               State 
            </MenuItem>
            {states.map((state) => (
              <MenuItem key={state.isoCode} value={state.name}>
                {state.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        {search.country === "India" && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <Select
              fullWidth
              displayEmpty
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              renderValue={(selected) => (selected ? selected : "District")}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            >
              <MenuItem value="">
                 district 
              </MenuItem>
              {districts.map((district) => (
                <MenuItem
                  key={district.id || district.name}
                  value={district.name}
                >
                  {district.name}
                </MenuItem>
              ))}
            </Select>
          </Grid>
        )}
        {(search.country !== "India" || search.country === "") && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <TextField
              placeholder="District"
              variant="outlined"
              name="district"
              fullWidth
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              onKeyPress={handleKeyPress}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            />
          </Grid>
        )}
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.city}
            onChange={(e) => handleInputChange("city", e.target.value)}
            renderValue={(selected) => (selected ? selected : "City")}
            sx={{ bgcolor: "white" }}
            disabled={!search.state}
          >
            <MenuItem value="">
               City 
            </MenuItem>
            {cities.map((city) => (
              <MenuItem key={city.id || city.name} value={city.name}>
                {city.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.month}
            onChange={(e) => handleInputChange("month", e.target.value)}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
               Select Month 
            </MenuItem>
            {monthValues.map((month) => (
              <MenuItem key={month.value} value={month.value}>
                {month.month}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.year}
            onChange={(e) => handleInputChange("year", e.target.value)}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
               Select Year 
            </MenuItem>
            {years.map((year) => (
              <MenuItem key={year} value={year}>
                {year}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid
          item
          xs={12}
          sm={4}
          md={12}
          lg={2}
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}
        >
          <Button
            variant="containedSecondary"
            color="secondary"
            sx={{
              width: "40px",
              minWidth: "40px !important",
            }}
            onClick={handleReset}
            disabled={loading}
          >
            <RestartAlt />
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={() => handleSearch(1)}
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SearchIcon />
              )
            }
            sx={{
              bgcolor: "#3f51b5",
              textTransform: "none",
              height: "40px",
              fontSize: "16px",
              maxWidth: {
                xs: "100%",
                sm: "150px",
              },
            }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default TournamentSearchForm;
