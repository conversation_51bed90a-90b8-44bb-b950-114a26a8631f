import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Typography,
  Box,
  Skeleton,
} from "@mui/material";

const SelectableTable = ({ data, selectedType, onSelectionChange, loading }) => {
  const [selectedRows, setSelectedRows] = useState([]);

  // Reset selection when data changes
  useEffect(() => {
    setSelectedRows([]);
    onSelectionChange([]);
  }, [data, selectedType]);

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedRows(data);
      onSelectionChange(data);
    } else {
      setSelectedRows([]);
      onSelectionChange([]);
    }
  };

  const handleSelectRow = (row) => {
    const isSelected = selectedRows.some(selected => getRowId(selected) === getRowId(row));
    let newSelection;
    
    if (isSelected) {
      newSelection = selectedRows.filter(selected => getRowId(selected) !== getRowId(row));
    } else {
      newSelection = [...selectedRows, row];
    }
    
    setSelectedRows(newSelection);
    onSelectionChange(newSelection);
  };

  const getRowId = (row) => {
    switch (selectedType) {
      case "player":
        return row.cbid || row.id;
      case "club":
        return row.clubId || row.id;
      case "tournament":
        return row.tournamentId || row.id;
      default:
        return row.id || row.userId;
    }
  };

  const isRowSelected = (row) => {
    return selectedRows.some(selected => getRowId(selected) === getRowId(row));
  };

  const getColumns = () => {
    switch (selectedType) {
      case "player":
        return [
          { id: "name", label: "Player Name" },
          { id: "cbid", label: "CBID" },
          { id: "email", label: "Email" },
          { id: "phoneNumber", label: "Phone" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "fideId", label: "FIDE ID" },
        ];
      case "club":
        return [
          { id: "clubName", label: "Club Name" },
          { id: "clubId", label: "Club ID" },
          { id: "email", label: "Email" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "city", label: "City" },
        ];
      case "tournament":
        return [
          { id: "tournamentName", label: "Tournament Name" },
          { id: "tournamentId", label: "Tournament ID" },
          { id: "startDate", label: "Start Date" },
          { id: "endDate", label: "End Date" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
        ];
      default:
        return [
          { id: "name", label: "Name" },
          { id: "userId", label: "User ID" },
          { id: "email", label: "Email" },
          { id: "phone", label: "Phone" },
          { id: "role", label: "Role" },
        ];
    }
  };

  const formatCellValue = (row, columnId) => {
    const value = row[columnId];
    
    // Handle date formatting
    if (columnId.includes("Date") && value) {
      return new Date(value).toLocaleDateString();
    }
    
    // Handle missing values
    if (!value) {
      return "-";
    }
    
    return value;
  };

  const columns = getColumns();
  const isAllSelected = data.length > 0 && selectedRows.length === data.length;
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < data.length;

  if (loading) {
    return (
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: "#CCBEF033" }}>
                <TableCell padding="checkbox">
                  <Skeleton variant="rectangular" width={24} height={24} />
                </TableCell>
                {columns.map((column) => (
                  <TableCell key={column.id}>
                    <Skeleton variant="text" />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell padding="checkbox">
                    <Skeleton variant="rectangular" width={24} height={24} />
                  </TableCell>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  if (data.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <Typography variant="h6" color="black">
          No results found. Please refine your search criteria.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow sx={{ bgcolor: "#CCBEF033" }}>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={isIndeterminate}
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                  color="primary"
                />
              </TableCell>
              {columns.map((column) => (
                <TableCell key={column.id} sx={{ fontWeight: "bold" }}>
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, index) => {
              const isSelected = isRowSelected(row);
              return (
                <TableRow
                  key={getRowId(row) || index}
                  hover
                  onClick={() => handleSelectRow(row)}
                  role="checkbox"
                  aria-checked={isSelected}
                  selected={isSelected}
                  sx={{
                    cursor: "pointer",
                    "&:nth-of-type(odd)": { backgroundColor: "#BEDDF026" },
                    "&:nth-of-type(even)": { backgroundColor: "#DAECF81F" },
                    "&.Mui-selected": {
                      backgroundColor: "#e3f2fd !important",
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={isSelected}
                      color="primary"
                      onChange={() => handleSelectRow(row)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </TableCell>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      {formatCellValue(row, column.id)}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Selection Summary */}
      <Box sx={{ p: 2, bgcolor: "#f5f5f5", borderTop: "1px solid #e0e0e0" }}>
        <Typography variant="body2" color="black">
          {selectedRows.length} of {data.length} items selected
        </Typography>
      </Box>
    </Paper>
  );
};

export default SelectableTable;
