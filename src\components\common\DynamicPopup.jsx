import { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Select,
  FormControl,
  FormHelperText,
  CircularProgress,
  Typography,
  Box,
  IconButton,
  Stack,
  Paper,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import { useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Controller, useForm } from "react-hook-form";

// Define validation schema using Zod
const validationSchema = z.object({
  ageCategory: z.string().min(1, { message: "Age category is required" }),
  genderCategory: z.string().min(1, { message: "Gender category is required" }),
  round: z.number().min(-1).refine(value => value >= 0, { 
    message: "Round is required" 
  }),
  file: z
    .instanceof(File, { message: "File is required" })
    .nullable()
    .refine((file) => file !== null, { message: "File is required" }),
});

export default function DynamicPopup({
  open,
  onClose,
  file_title,
  tournament,
  type,
}) {
  const [loading, setLoading] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [data, setData] = useState(null); // Temporarily hold form data
  const toast = UseToast();
  const { title: id } = useParams();
  const [ageCategory, setAgeCategory] = useState([]);

  const genderOptions =
    tournament && tournament?.tournamentCategory === "open"
      ? ["male", "female"]
      : [tournament?.tournamentCategory];
  // Modify roundOptions to include Starting and Final Ranking for ranking screen (type === 1)
  const roundOptions = type === 1
    ? [
      { value: 0, label: "Starting Rank" },
      ...Array.from({ length: tournament?.numberOfRounds }, (_, i) => ({ value: i + 1, label: `Round ${i + 1}` })),
      { value: 9999, label: "Final Rank" }
    ]
    : Array.from({ length: tournament?.numberOfRounds }, (_, i) => ({ value: i + 1, label: `Round ${i + 1}` }));

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      ageCategory: "",
      genderCategory: "",
      round: -1, // Changed from 0 to -1 to match the placeholder value
      file: null,
    },
  });
  const gendervalue = watch("genderCategory");

  useEffect(() => {
    if (tournament) {
      if (gendervalue === "male") {
        setAgeCategory(tournament?.maleAgeCategory);
      } else if (gendervalue === "female") {
        setAgeCategory(tournament?.femaleAgeCategory);
      }
    }
  }, [gendervalue]);

  // Get selected file from form values
  const selectedFile = watch("file");
  const handleClose = () => {
    onClose(false);
    reset({
      ageCategory: "",
      genderCategory: "",
      round: -1, // Changed from 0 to -1 to match the placeholder value
      file: null,
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setValue("file", file, { shouldValidate: true, shouldDirty: true });
    }
  };

  const handleFileRemove = () => {
    setValue("file", null);
  };
  const confirmAndUpload = async () => {
    try {
      const formData = new FormData();
      formData.append("file", data.file);
      formData.append("tournament_id", id);
      formData.append("round_id", data.round);
      formData.append("age_category", data.ageCategory);
      formData.append("gender_category", data.genderCategory);
      formData.append("deleteAfterProcessing", true);

      const endpoint =
        type === 0 ? `pairing-import/upload` : `ranking-import/upload`;

      setLoading(true);
      const response = await Client.post(endpoint, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        toast.success("Upload successful");
        onClose(false);
        reset();
        setConfirmOpen(false);
      } else {
        toast.error(response.data.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error submitting data:", error);
      setConfirmOpen(false);
      toast.error("An error occurred while submitting data");
    } finally {
      setLoading(false);
    }
  };
  const onSubmit = async (data) => {
    setData(data);
    setConfirmOpen(true);
  };

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {`Upload File For ${file_title}`}
        <IconButton onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <FormControl size="small" fullWidth error={!!errors.genderCategory}>
              <Typography fontSize={15}>Select gender Category</Typography>
              <Controller
                name="genderCategory"
                control={control}
                render={({ field }) => (
                  <Select {...field} error={!!errors.genderCategory}>
                    <MenuItem value={0}>Select gender Category...</MenuItem>
                    {genderOptions &&
                      genderOptions.map((option) => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                  </Select>
                )}
              />
              {errors.genderCategory && (
                <FormHelperText>{errors.genderCategory.message}</FormHelperText>
              )}
            </FormControl>
            {/* Age Category Dropdown */}
            <FormControl size="small" fullWidth error={!!errors.ageCategory}>
              <Typography fontSize={15}>Select Age Category</Typography>
              <Controller
                name="ageCategory"
                control={control}
                render={({ field }) => (
                  <Select {...field} error={!!errors.ageCategory}>
                    <MenuItem value={0}>Select age category...</MenuItem>
                    {ageCategory &&
                      ageCategory.map((option) => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                  </Select>
                )}
              />
              {errors.ageCategory && (
                <FormHelperText>{errors.ageCategory.message}</FormHelperText>
              )}
            </FormControl>

            {/* Round Dropdown */}
            <FormControl size="small" fullWidth error={!!errors.round}>
              <Typography fontSize={15}>Select Round</Typography>
              <Controller
                name="round"
                control={control}
                render={({ field }) => (
                  <Select 
                    {...field} 
                    error={!!errors.round}
                    onChange={(e) => {
                      // Convert the value to a number before setting it
                      const numValue = Number(e.target.value);
                      field.onChange(numValue);
                    }}
                  >
                    <MenuItem value={-1}>Select a round...</MenuItem>
                    {roundOptions.map((option) => (
                      <MenuItem key={option.value || option} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
              {errors.round && (
                <FormHelperText>{errors.round.message}</FormHelperText>
              )}
            </FormControl>

            {/* File Upload */}
            <Box>
              <input
                type="file"
                accept=".xls,.xlsx,.xml"
                id="file-input"
                style={{ display: "none" }}
                onChange={handleFileChange}
              />
              <label htmlFor="file-input">
                <Button
                  size="small"
                  variant="outlined"
                  component="span"
                  startIcon={<UploadFileIcon sx={{ color: "blue" }} />}
                >
                  Choose File
                </Button>
              </label>
              <Typography variant="caption" sx={{ ml: 2 }}>
                {selectedFile ? selectedFile.name : "No file selected"}
              </Typography>
              {errors.file && (
                <FormHelperText error>{errors.file.message}</FormHelperText>
              )}

              {selectedFile && (
                <Paper sx={{ mt: 1, p: 1 }}>
                  <Typography fontSize={13}>
                    <strong>File Name:</strong> {selectedFile.name}
                  </Typography>
                  <Typography fontSize={13}>
                    <strong>Size:</strong>{" "}
                    {Math.round(selectedFile.size / 1024)} KB
                  </Typography>
                  <Typography fontSize={13}>
                    <strong>Type:</strong> {selectedFile.type}
                  </Typography>
                  <Button
                    onClick={handleFileRemove}
                    sx={{ mt: 2 }}
                    variant="outlined"
                    size="small"
                  >
                    <Typography fontSize={15}>
                      <strong>Remove File</strong>
                    </Typography>
                  </Button>
                </Paper>
              )}
            </Box>

            {/* Loading Indicator */}
            {loading && (
              <Box display="flex" justifyContent="center">
                <CircularProgress />
              </Box>
            )}
          </Stack>
        </form>
      </DialogContent>

      <DialogActions>
        <Button size="small" onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button
          size="small"
          type="submit"
          variant="contained"
          disabled={loading}
          onClick={handleSubmit(onSubmit)}
        >
          Submit
        </Button>
      </DialogActions>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm Upload</DialogTitle>
        <DialogContent>
          <Typography>Are you sure you want to upload this file?</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmOpen(false)}
            variant="outlined"
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={confirmAndUpload}
            variant="contained"
            size="small"
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : "OK"}
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
}
