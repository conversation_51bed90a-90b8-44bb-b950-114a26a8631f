import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
  Chip,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import UserSearchForm from "../../components/admin/UserSearchForm";
import SelectableTable from "../../components/admin/SelectableTable";
import UseToast from "../../lib/hooks/UseToast";

const MessagingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  // Extract mode from URL path (email, sms, whatsapp)
  const mode = location.pathname.split("/").pop();

  const [selectedType, setSelectedType] = useState("player");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);

  // Reset selected users when type changes
  useEffect(() => {
    setSelectedUsers([]);
    setSearchResults([]);
    setSearchPerformed(false);
  }, [selectedType]);

  const handleTypeChange = (event) => {
    setSelectedType(event.target.value);
  };

  const handleSearchResults = (results) => {
    setSearchResults(results);
    setSearchPerformed(true);
  };

  const handleUserSelection = (users) => {
    setSelectedUsers(users);
  };

  const handleProceedToCompose = () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select at least one recipient");
      return;
    }

    // Extract relevant information based on selected type
    const extractedInfo = selectedUsers.map((user) => {
      const baseInfo = {
        id: user.id || user.cbid || user.clubId,
        name: user.name || user.playerName || user.clubName,
        email: user.email,
        phone: user.phoneNumber || user.mobile,
      };

      // Add type-specific information
      switch (selectedType) {
        case "player":
          return {
            ...baseInfo,
            cbid: user.cbid,
            fideId: user.fideId,
            aicfId: user.aicfId,
            type: "player",
          };
        case "club":
          return {
            ...baseInfo,
            clubId: user.clubId,
            type: "club",
          };
        case "tournament":
          return {
            ...baseInfo,
            tournamentId: user.tournamentId,
            type: "tournament",
          };
        default:
          return {
            ...baseInfo,
            type: selectedType,
          };
      }
    });

    // Navigate to compose page with selected users data
    navigate(`/admin/dashboard/${mode}/compose`, {
      state: {
        selectedUsers: extractedInfo,
        selectedType,
        mode,
      },
    });
  };

  const getPageTitle = () => {
    const modeTitle = mode.charAt(0).toUpperCase() + mode.slice(1);
    return `${modeTitle} Messaging`;
  };

  const getTypeOptions = () => [
    { value: "player", label: "Players" },
    { value: "club", label: "Clubs" },
    { value: "user", label: "Users" },
    { value: "arbiter", label: "Arbiter" },
    { value: "tournament", label: "Tournaments" },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          {getPageTitle()}
        </Typography>
        <Typography sx={{ fontSize: "16px", textAlign: "start" }} color="black">
          Select recipients and compose your {mode} message
        </Typography>
      </Box>

      {/* Type Selection */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Select Recipient Type
        </Typography>
        <FormControl fullWidth sx={{ maxWidth: 300 }}>
          {/* <InputLabel sx={{color:"black"}}>Recipient Type</InputLabel> */}
          <Select value={selectedType} onChange={handleTypeChange}>
            {getTypeOptions().map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Search Form */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Step 2: Search and Filter Recipients
        </Typography>
        <UserSearchForm
          selectedType={selectedType}
          onSearchResults={handleSearchResults}
          loading={loading}
          setLoading={setLoading}
        />
      </Paper>

      {/* Results Table */}
      {searchPerformed && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Step 3: Select Recipients
          </Typography>
          <SelectableTable
            data={searchResults}
            selectedType={selectedType}
            onSelectionChange={handleUserSelection}
            loading={loading}
          />
        </Paper>
      )}

      {/* Selected Users Summary */}
      {selectedUsers.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Selected Recipients ({selectedUsers.length})
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
            {selectedUsers.slice(0, 10).map((user, index) => (
              <Chip
                key={index}
                label={user.name || user.playerName || user.clubName}
                variant="outlined"
                size="small"
              />
            ))}
            {selectedUsers.length > 10 && (
              <Chip
                label={`+${selectedUsers.length - 10} more`}
                variant="outlined"
                size="small"
                color="primary"
              />
            )}
          </Box>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleProceedToCompose}
            sx={{ mt: 2 }}
          >
            Proceed to Compose {mode.toUpperCase()}
          </Button>
        </Paper>
      )}
    </Container>
  );
};

export default MessagingPage;
