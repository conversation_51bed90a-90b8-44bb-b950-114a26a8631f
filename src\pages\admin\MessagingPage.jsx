import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
  Chip,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import PlayersSearchForm from "../../components/common/PlayersSearchForm";
import ClubSearchForm from "../../components/common/ClubSearchForm";
import ArbiterSearchForm from "../../components/form/ArbiterSearchForm";
import TournamentSearchForm from "../../components/common/TournamentSearchForm";
import SelectableTable from "../../components/admin/SelectableTable";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const MessagingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  // Extract mode from URL path (email, sms, whatsapp)
  const mode = location.pathname.split("/").pop();

  const [selectedType, setSelectedType] = useState("player");
  const [searchResults, setSearchResults] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [search, setSearch] = useState({});
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Reset selected users when type changes
  useEffect(() => {
    setSelectedUsers([]);
    setSearchResults([]);
    setSearchPerformed(false);
    setSearch({});
    setPage(1);
  }, [selectedType]);

  const handleTypeChange = (event) => {
    setSelectedType(event.target.value);
  };

  const handleUserSelection = (users) => {
    setSelectedUsers(users);
  };

  // Search function that will be called by the search forms
  const handleSearch = async (pageNum = 1) => {
    setLoading(true);
    setPage(pageNum);

    try {
      let endpoint = "";
      const baseParams = { page: pageNum, limit: 50 };
      let searchParams = {};

      const addIfExists = (key, value) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams[key] = value;
        }
      };

      switch (selectedType) {
        case "player":
          endpoint = "/player";
          addIfExists("playerName", search.playerName);
          addIfExists("playerId", search.playerId);
          break;
        case "club":
          endpoint = "/club";
          addIfExists("clubName", search.clubName);
          break;
        case "arbiter":
          endpoint = "/arbiter";
          addIfExists("arbiterName", search.arbiterName);
          addIfExists("arbiterId", search.arbiterId);
          break;
        case "tournament":
          endpoint = "/tournament";
          addIfExists("title", search.title);
          addIfExists("tournamentType", search.tournamentType);
          addIfExists("tournamentCategory", search.tournamentCategory);
          addIfExists("age", search.age);
          addIfExists("month", search.month);
          addIfExists("year", search.year);
          break;
        default:
          toast.error("Invalid search type");
          return;
      }

      // Common fields
      addIfExists("country", search.country);
      addIfExists("state", search.state);
      addIfExists("district", search.district);
      addIfExists("city", search.city);

      const params = { ...baseParams, ...searchParams };

      const response = await Client.get(endpoint, { params });

      if (response.data.success) {
        const responseData = response.data.data || [];
        const results =
          responseData.players ||
          responseData.clubs ||
          responseData.arbiters ||
          responseData.tournaments ||
          [];
        setSearchResults(results);
        setTotalPages(response.data.totalPages || 1);
        setSearchPerformed(true);

        if (results.length === 0) {
          toast.info("No results found");
        }
      } else {
        toast.error(response.data.message || "Search failed");
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("Search failed. Please try again.");
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Reset function for search forms
  const handleReset = () => {
    setSearch({});
    setSearchResults([]);
    setSelectedUsers([]);
    setSearchPerformed(false);
    setPage(1);
  };

  const handleProceedToCompose = () => {
    if (selectedUsers.length === 0) {
      toast.error("Please select at least one recipient");
      return;
    }

    // Extract relevant information based on selected type
    const extractedInfo = selectedUsers.map((user) => {
      const baseInfo = {
        id: user.id || user.cbid || user.clubId,
        name: user.name || user.playerName || user.clubName,
        email: user.email,
        phone: user.phoneNumber || user.mobile,
      };

      // Add type-specific information
      switch (selectedType) {
        case "player":
          return {
            ...baseInfo,
            cbid: user.cbid,
            fideId: user.fideId,
            aicfId: user.aicfId,
            type: "player",
          };
        case "club":
          return {
            ...baseInfo,
            clubId: user.clubId,
            type: "club",
          };
        case "tournament":
          return {
            ...baseInfo,
            tournamentId: user.tournamentId,
            type: "tournament",
          };
        default:
          return {
            ...baseInfo,
            type: selectedType,
          };
      }
    });

    // Navigate to compose page with selected users data
    navigate(`/admin/dashboard/${mode}/compose`, {
      state: {
        selectedUsers: extractedInfo,
        selectedType,
        mode,
      },
    });
  };

  const getPageTitle = () => {
    const modeTitle = mode.charAt(0).toUpperCase() + mode.slice(1);
    return `${modeTitle} Messaging`;
  };

  const getTypeOptions = () => [
    { value: "player", label: "Players" },
    { value: "club", label: "Clubs" },
    { value: "arbiter", label: "Arbiters" },
    { value: "tournament", label: "Tournaments" },
  ];

  // Render the appropriate search form based on selected type
  const renderSearchForm = () => {
    const commonProps = {
      search,
      setSearch,
      handleSearch,
      loading,
      handleReset,
    };

    switch (selectedType) {
      case "player":
        return <PlayersSearchForm {...commonProps} />;
      case "club":
        return <ClubSearchForm {...commonProps} />;
      case "arbiter":
        return <ArbiterSearchForm {...commonProps} />;
      case "tournament":
        return <TournamentSearchForm {...commonProps} />;
      default:
        return <PlayersSearchForm {...commonProps} />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          {getPageTitle()}
        </Typography>
        <Typography sx={{ fontSize: "16px", textAlign: "start" }} color="black">
          Select recipients and compose your {mode} message
        </Typography>
      </Box>

      {/* Type Selection */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Select Recipient Type
        </Typography>
        <FormControl fullWidth sx={{ maxWidth: 300 }}>
          {/* <InputLabel sx={{color:"black"}}>Recipient Type</InputLabel> */}
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            sx={{ "& .MuiSelect-select": { padding: "8px 14px" } }}
          >
            {getTypeOptions().map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
      <Box sx={{ my: 2 }}>{renderSearchForm()}</Box>

      {/* Results Table */}
      {/* {searchPerformed && ( */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <SelectableTable
          data={searchResults}
          selectedType={selectedType}
          onSelectionChange={handleUserSelection}
          loading={loading}
        />
      </Paper>
      {/* )} */}

      {/* Selected Users Summary */}
      {selectedUsers.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Selected Recipients ({selectedUsers.length})
          </Typography>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
            {selectedUsers.slice(0, 10).map((user, index) => (
              <Chip
                key={index}
                label={user.name || user.playerName || user.clubName}
                variant="outlined"
                size="small"
              />
            ))}
            {selectedUsers.length > 10 && (
              <Chip
                label={`+${selectedUsers.length - 10} more`}
                variant="outlined"
                size="small"
                color="primary"
              />
            )}
          </Box>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleProceedToCompose}
            sx={{ mt: 2 }}
          >
            Proceed to Compose {mode.toUpperCase()}
          </Button>
        </Paper>
      )}
    </Container>
  );
};

export default MessagingPage;
