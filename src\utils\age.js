import { useMemo } from 'react';
import dayjs from 'dayjs'; // use dayjs for reliable date handling

const getEligibleCategories = (dob, gender, maleAgeCategory, femaleAgeCategory) => {
  const age = dayjs().diff(dayjs(dob), 'year');

  const categories = gender === 'male' ? maleAgeCategory : femaleAgeCategory;

   const eligible = categories.filter(category => {
    if (category === 'OPEN') return age >= 18;
    const match = category.match(/^U(\d+)$/);
    if (match) {
      const maxAge = parseInt(match[1], 10);
      return age <= maxAge;
    }
    return false; // skip if unrecognized format
  });
  return eligible.length > 0 ? eligible : false
};

export default getEligibleCategories
