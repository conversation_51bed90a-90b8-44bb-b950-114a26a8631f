import React, { useEffect, useState } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Button, 
  Divider,
  Al<PERSON>,
  Link
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import BackButton from '../components/common/BackButton';

const PaymentFailurePage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [errorType, setErrorType] = useState('');
  const [txnId, setTxnId] = useState('');

  useEffect(() => {
    // Parse URL parameters
    const search = location.search;
    
    // Extract error type
    const errorMatch = search.match(/error=([^&?]+)/);
    const errorParam = errorMatch ? decodeURIComponent(errorMatch[1]) : '';
    setErrorType(errorParam);
    
    // Extract transaction ID if available
    const txnidMatch = search.match(/txnid=([^&?]+)/);
    const txnid = txnidMatch ? txnidMatch[1] : '';
    setTxnId(txnid);
}, [location.search]);

  // Get error message based on error type
  const getErrorMessage = () => {
    switch (errorType) {
      case 'invalid_hash':
        return 'The payment verification failed due to security reasons. This could be due to data tampering or an invalid payment request.';
      case 'server_error':
        return 'Our server encountered an error while processing your payment. This is a temporary issue on our end.';
      case 'payment_declined':
        return 'Your payment was declined by the payment provider. This could be due to insufficient funds or card restrictions.';
      case 'payment_cancelled':
        return 'You cancelled the payment process before it was completed.';
      default:
        return 'Your payment could not be processed. Please try again or contact our support team for assistance.';
    }
  };

  // Get error title based on error type
  const getErrorTitle = () => {
    switch (errorType) {
      case 'invalid_hash':
        return 'Security Verification Failed';
      case 'server_error':
        return 'Server Error';
      case 'payment_declined':
        return 'Payment Declined';
      case 'payment_cancelled':
        return 'Payment Cancelled';
      default:
        return 'Payment Failed';
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <BackButton />
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          borderRadius: 2,
          border: '1px solid #e0e0e0',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ErrorOutlineIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
          <Typography variant="h4" component="h1" color="error.main">
            {getErrorTitle()}
          </Typography>
        </Box>

        <Alert severity="error" sx={{ mb: 3,fontSize: 16 }}>
          {getErrorMessage()}
        </Alert>

        {txnId && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1">
              <strong>Transaction Reference:</strong> {txnId}
            </Typography>
            <Typography variant="h6" color="black" sx={{ mt: 1 ,fontSize: 16 }}>
              Please save this reference number for communication with our support team.
            </Typography>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Typography variant="h6" gutterBottom>
          What should I do now?
        </Typography>

        <Box component="ul" sx={{ pl: 2 }}>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            Try making the payment again with a different payment method
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            Check if your card has sufficient balance or if there are any restrictions
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            Contact your bank to ensure there are no blocks on online transactions
          </Typography>
          <Typography component="li" variant="body1">
            Reach out to our support team for assistance
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ flex: 1 ,fontSize: 16 }}
          >
            Go Dashboard
          </Button>
          
          <Button
            variant="contained"
            color="primary"
            startIcon={<SupportAgentIcon />}
            component={Link}
            href="mailto:<EMAIL>?subject=Payment%20Failure%20Support"
            target="_blank"
            sx={{ 
              flex: 1,
              fontSize: 16,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            Contact Support
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentFailurePage;
