import { <PERSON>, Button, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import DynamicTable from "../common/DynamicTable";
import BackButton from "../common/BackButton";
import { Client } from "../../api/client";
import { useParams, useSearchParams } from "react-router-dom";

import { Height } from "@mui/icons-material";

const SelectedPlayers = () => {
  const [players, setPlayers] = useState([]);
  const [searchParams] = useSearchParams();
  const show = searchParams.get("show");
  const showDetails = show === "true" ? true : false;
  const [data, setData] = useState({});

  const columns = [
    { id: "sno", label: "S.No", format: (value, item, index) => index + 1 },
    { id: "ageCategory", label: "AgeCategory" },
    { id: "gender", label: "Gender" },
    { id: "playerTitle", label: "Title" },
    { id: "name", label: "Name" },
    { id: "fideId", label: "FIDE ID" },
    { id: "aicfId", label: "National ID" },
    { id: "stateId", label: "State ID" },
    { id: "districtId", label: "District ID" },
  ];

  const { title, id } = useParams();

  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchRegisteredPlayersList = async () => {
      setLoading(true);
      try {
        const response = await Client.get(
          `/tournament/${title}/register/bulk-register/${id}`
        );
        if (!response.data.success) return;
        setData(response.data.data);
        setPlayers(response.data.data.players);
      } catch (error) {
        console.error("Error fetching players:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRegisteredPlayersList();
  }, []);

  const handleRegister = async () => {
    if (showDetails) return;
    try {
      const response = await Client.post(`/payment/bulk-initiate`, {
        bulkRegistrationId: data.bulkRegistrationId,
      });
      if (!response.data.success) {
        console.error(response.data.message || "Payment initiation failed");
        return;
      }

      const responseData = response.data.data;

      const form = document.createElement("form");
      form.method = "post";
      form.action = responseData.paymentUrl;

      Object.entries(responseData.formData).forEach(([key, value]) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = value;
        form.appendChild(input);
      });

      document.body.appendChild(form);
      form.submit();
      document.body.removeChild(form);
    } catch (error) {
      console.error("Error initiating payment:", error);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70vh" }}>
      <BackButton />

      <DynamicTable
        columns={columns}
        data={players}
        loading={loading}
        page={1}
        totalPages={1}
        onPageChange={() => {}}
        idField="fideId"
        showDetailsButton={false}
      
      />
      {!showDetails && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            mt: 2,
          }}
        >
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={handleRegister}
          >
            Register Now
          </Button>
        </Box>
      )}
    </Container>
  );
};

export default SelectedPlayers;
