import { Box, CircularProgress, Typography, Paper } from "@mui/material";
import { GoogleMap, useJsApi<PERSON>oader, Mark<PERSON> } from "@react-google-maps/api";
import { useState, useEffect, useCallback } from "react";
import { useController, useWatch } from "react-hook-form";

// Define the API key at module level
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_MAPS_API_KEY;

// Map container style
const mapContainerStyle = {
  width: "100%",
  height: "400px",
  borderRadius: "8px",
};

// Default center
const DEFAULT_CENTER = { lat: 20.5937, lng: 78.9629 };

const AutoNavigateMapComponent = ({
  control,
  name,
  countryField,
  stateField,
  cityField,
}) => {
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    id: "google-map-script",
  });

  const [selectedLocation, setSelectedLocation] = useState(null);
  const [map, setMap] = useState(null);
  const [isGeocoding, setIsGeocoding] = useState(false);
  const [locationInfo, setLocationInfo] = useState("");

  // Watch the dropdown values
  const country = useWatch({ control, name: countryField });
  const state = useWatch({ control, name: stateField });
  const city = useWatch({ control, name: cityField });

  const {
    field,
    fieldState: { error: fieldError },
  } = useController({
    name,
    control,
    defaultValue: null,
  });

  // Enhanced map options
  const mapOptions = {
    zoomControl: true,
    streetViewControl: true,
    mapTypeControl: true,
    fullscreenControl: true,
    gestureHandling: "greedy",
    keyboardShortcuts: true,
  };

  // Update location and form field
  const updateLocation = useCallback(
    (location, address = "") => {
      setSelectedLocation(location);
      const locationUrl = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lng}`;
      field.onChange(locationUrl);

      setLocationInfo(address);
    },
    [field]
  );

  // Geocode location using Google Geocoding API
  const geocodeLocation = useCallback(
    async (address) => {
      if (!window.google || !address.trim()) return;

      setIsGeocoding(true);

      try {
        const geocoder = new window.google.maps.Geocoder();

        geocoder.geocode(
          {
            address: address,
            region: country?.code || country?.value || "IN", // Default to India or use country code
          },
          (results, status) => {
            setIsGeocoding(false);

            if (status === "OK" && results && results[0]) {
              const result = results[0];
              const location = {
                lat: result.geometry.location.lat(),
                lng: result.geometry.location.lng(),
              };

              updateLocation(location, result.formatted_address);

              if (map) {
                map.panTo(location);
                // Set zoom based on location type
                let zoomLevel = 10;
                if (city) zoomLevel = 13;
                else if (state) zoomLevel = 8;
                else if (country) zoomLevel = 6;

                map.setZoom(zoomLevel);
              }
            } else {
              console.warn("Geocoding failed:", status);
            }
          }
        );
      } catch (error) {
        console.error("Geocoding error:", error);
        setIsGeocoding(false);
      }
    },
    [country, map, updateLocation]
  );

  // Build address string from dropdown values
  const buildAddressString = useCallback(() => {
    const parts = [];

    if (city?.label || city?.value || city) {
      parts.push(typeof city === "object" ? city.label || city.value : city);
    }
    if (state?.label || state?.value || state) {
      parts.push(
        typeof state === "object" ? state.label || state.value : state
      );
    }
    if (country?.label || country?.value || country) {
      parts.push(
        typeof country === "object" ? country.label || country.value : country
      );
    }

    return parts.join(", ");
  }, [city, state, country]);

  // Auto-navigate when dropdown values change
  useEffect(() => {
    if (!isLoaded) return;

    const address = buildAddressString();
    if (address) {
      geocodeLocation(address);
    }
  }, [city, state, country, isLoaded, buildAddressString, geocodeLocation]);

  const onLoad = useCallback((map) => {
    setMap(map);
  }, []);

  const onUnmount = useCallback(() => {
    setMap(null);
  }, []);

  // Handle manual map clicks
  const handleMapClick = useCallback(
    (event) => {
      const location = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      updateLocation(
        location,
        `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`
      );
    },
    [updateLocation]
  );

  // Loading state
  if (!isLoaded) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={400}
        bgcolor="grey.100"
        borderRadius={2}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (loadError) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={400}
        bgcolor="grey.100"
        borderRadius={2}
        p={2}
      >
        <Typography color="error" align="center">
          Failed to load Google Maps: {loadError.message}
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Status Bar */}
      {(isGeocoding || locationInfo) && (
        <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: "grey.50" }}>
          {isGeocoding ? (
            <Box
              display="flex"
              alignItems="center"
              justifyContent={"center"}
              gap={1}
            >
              <CircularProgress size={16} />
              <Typography
                variant="body2"
                color="black"
                sx={{ fontSize: 14, textAlign: "center" }}
              >
                Locating {buildAddressString()}...
              </Typography>
            </Box>
          ) : (
            <Typography variant="body2" color="black" sx={{ fontSize: 14 }}>
              📍 {locationInfo}
            </Typography>
          )}
        </Paper>
      )}

      {/* Map */}
      <Box position="relative">
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={selectedLocation || DEFAULT_CENTER}
          zoom={selectedLocation ? 10 : 6}
          onClick={handleMapClick}
          onLoad={onLoad}
          onUnmount={onUnmount}
          options={mapOptions}
        >
          {selectedLocation && (
            <Marker
              position={selectedLocation}
              animation={window.google?.maps?.Animation?.DROP}
            />
          )}
        </GoogleMap>

        {/* Coordinates Overlay */}
        {selectedLocation && (
          <Paper
            elevation={3}
            sx={{
              position: "absolute",
              top: 10,
              right: 10,
              p: 1.5,
              bgcolor: "rgba(255, 255, 255, 0.95)",
              minWidth: 150,
            }}
          >
            <Typography variant="caption" display="block" fontWeight="bold">
              Coordinates:
            </Typography>
            <Typography variant="caption" display="block">
              Lat: {selectedLocation.lat.toFixed(6)}
            </Typography>
            <Typography variant="caption" display="block">
              Lng: {selectedLocation.lng.toFixed(6)}
            </Typography>
          </Paper>
        )}
      </Box>

      {/* Error Display */}
      {fieldError && (
        <Typography color="error" variant="body2" sx={{ mt: 1 }}>
          {fieldError.message}
        </Typography>
      )}

      {/* Instructions */}
      <Typography variant="body2" color="black" sx={{ mt: 1, fontSize: 12 }}>
        Map automatically centers when you select Country/State/City from
        dropdowns above. Click anywhere on the map to fine-tune the location
        pin.
      </Typography>
    </Box>
  );
};

export default AutoNavigateMapComponent;
