import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Divider,
  Breadcrumbs,
  Link,
  Alert,
  CircularProgress,
  <PERSON>,
  But<PERSON>,
} from "@mui/material";
import {
  Home as HomeIcon,
  EmojiEvents as TournamentIcon,
  Group as GroupIcon,
} from "@mui/icons-material";
import { Link as RouterLink, useNavigate, useParams } from "react-router-dom";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import PlayerSearchForm from "../../components/registration/PlayerSearchForm";
import PlayerTable from "../../components/registration/PlayerTable";
import BackButton from "../../components/common/BackButton";

/**
 * Page for club bulk registration of players for a tournament
 */
const BulkRegistrationPage = () => {
  const { title: id } = useParams();
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",

    ageCategory: "",
    genderCategory: "",
  });

  const navigate = useNavigate();
  const toast = UseToast();

  const [tournamentDetails, setTournamentDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  const limit = 10;

  const [searchResults, setSearchResults] = useState({
    players: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 0,
  });
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  // Add state for bulk registration info
  const [bulkRegistrationInfo, setBulkRegistrationInfo] = useState(null);
  const checkPendingRegistrations = async () => {
    try {
      const response = await Client.get(
        `/tournament/${id}/register/pending-bulk-registers`
      );
      if (response.data.success) {
        // Check if we have data with bulk registration info
        if (response.data.data) {
          // Store the bulk registration ID for later use
          const bulkRegistrationId = response.data.data.bulkRegistrationId;
          const registrationStatus =
            response.data.data.registrationStatus || "pending";

          // Set the bulk registration info in state
          setBulkRegistrationInfo({
            id: response.data.data.id,
            bulkRegistrationId,
            registrationStatus,
            tournamentId: response.data.data.tournamentId,
          });

          // Get the player list
          const playerList = response.data.data.playerList || [];

          // Format players with necessary properties
          const formattedPlayers = playerList.map((player) => ({
            cbid: player.cbid,
            playerId: player.playerId,
            ageCategory: player.ageCategory,
            genderCategory: player.genderCategory,
            playerName: player.playerName || "Unknown Player",
            // Individual players don't have status, they inherit from the bulk registration
            bulkRegistrationId,
          }));

          setSelectedPlayers(formattedPlayers);
        } else {
          setSelectedPlayers([]);
          setBulkRegistrationInfo(null);
        }
      }
    } catch (error) {
      console.error("Error checking bulk registration status:", error);
      toast.error("Failed to check pending bulk registrations");
    }
  };
  useEffect(() => {
    checkPendingRegistrations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const handleSubmitList = async () => {
    try {
      if (selectedPlayers.length === 0) {
        toast.warning("Please select at least one player to register");
        return;
      }

      // Format the player list for the API
      const playerList = selectedPlayers.map((player) => ({
        playerId: player.playerId,
        cbid: player.cbid,
        ageCategory: player.ageCategory,
        genderCategory: player.genderCategory,
      }));

      let response;

      // Check if we're updating an existing bulk registration or creating a new one
      if (
        bulkRegistrationInfo &&
        bulkRegistrationInfo.registrationStatus === "pending"
      ) {
        // Update existing bulk registration
        const payload = {
          bulkRegistrationId: bulkRegistrationInfo.bulkRegistrationId,
          tournamentId: id,
          players: playerList,
        };
        response = await Client.put(
          `/tournament/${id}/register/bulk-register`,
          payload
        );

        if (response.data.success) {
          toast.success(
            `Updated pending registration #${bulkRegistrationInfo.bulkRegistrationId} with ${selectedPlayers.length} players`
          );
          setSelectedPlayers([]);
          // Redirect to registration list page
          navigate(`register/${bulkRegistrationInfo.bulkRegistrationId}`);
        } else {
          toast.error(
            response.data.message || "Failed to update pending registration"
          );
        }
      } else {
        // Create new bulk registration
        const payload = {
          tournamentId: id,
          players: playerList,
        };
        response = await Client.post(
          `/tournament/${id}/register/bulk-register`,
          payload
        );

        if (response.data.success) {
          const brId = response.data.data.bulkRegistrationId;
          toast.success(
            `Created new pending registration #${brId} with ${selectedPlayers.length} players`
          );
          setSelectedPlayers([]);
          // Redirect to registration list page
          navigate(`register/${brId}`);
        } else {
          toast.error(
            response.data.message || "Failed to create new bulk registration"
          );
        }
      }
    } catch (error) {
      console.error("Error processing bulk registration:", error);
      toast.error("An error occurred while processing the bulk registration");
    }
  };

  // Fetch tournament details on component mount
  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${id}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);

          // After fetching tournament details, check for pending registrations
          await checkPendingRegistrations();
        } else {
          toast.error(
            response.data.message || "Failed to fetch tournament details"
          );
          // navigate("/tournaments");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("An error occurred while fetching tournament details");
        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };

    fetchTournamentDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);
  // Reset search form
  const handleReset = () => {
    setSearch({
      ...search,
      playerName: "",
      playerId: "",
    });
  };
  // Handle search form submission
  // Handle search form submission
  const handleSearch = async (page = 1) => {
    // Ensure page is a number
    const pageNumber = typeof page === "object" ? 1 : Number(page);

    setLoading(true);
    try {
      // Extract CBIDs from selected players to filter them out from search results
      const selectedCbids = selectedPlayers
        .map((player) => player.cbid)
        .filter(Boolean);
      const params = {
        name: search.playerName,
        playerId: search.playerId,
        ageCategory: search.ageCategory,
        genderCategory: search.genderCategory,
        page: pageNumber,
        limit,
        // Add selected CBIDs to filter them out from search results
        excludeCbids:
          selectedCbids.length > 0 ? selectedCbids.join(",") : undefined,
        // Add tournament ID to exclude already registered players
        tournamentId: id,
      };
      const response = await Client.get("/club/members/players", { params });
      if (response.data.success) {
        const players = response.data.data.players || [];
        const total = response.data.data.total || 0;
        // Force totalPages to be at least 1 if there are any players
        let totalPages = response.data.data.totalPages;

        if (!totalPages || totalPages < 1) {
          totalPages = Math.max(1, Math.ceil(total / limit));
        }

        setSearchResults({
          players,
          totalCount: total,
          currentPage: pageNumber,
          totalPages,
        });
      } else {
        toast.error(response.data.message || "Failed to search players");
        setSearchResults({
          players: [],
          totalCount: 0,
          currentPage: 1,
          totalPages: 0,
        });
      }
    } catch (error) {
      console.error("Error searching players:", error);
      toast.error("An error occurred while searching for players");
      setSearchResults({
        players: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0,
      });
    } finally {
      setLoading(false);
    }
  };
  // Initial data loading
  useEffect(() => {
    handleSearch(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle page change in search results
  const handlePageChange = (page) => {
    // Ensure page is a number
    const pageNumber = typeof page === "object" ? 1 : Number(page);

    if (pageNumber !== searchResults.currentPage) {
      handleSearch(pageNumber);
    }
  };

  // Handle player selection
  const handleSelectPlayer = (players) => {
    // Update the selected players
    setSelectedPlayers(players);
    // The useEffect will handle refreshing the search
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
      <BackButton />
      {/* Player Search Form */}
      <PlayerSearchForm
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
        
      />

      <PlayerTable
        players={searchResults.players}
        loading={loading}
        search={search}
        totalCount={searchResults.totalCount}
        currentPage={searchResults.currentPage}
        totalPages={searchResults.totalPages}
        onPageChange={handlePageChange}
        onSelectPlayer={handleSelectPlayer}
        selectedPlayers={selectedPlayers}
        tournamentDetails={tournamentDetails}
        bulkRegistrationInfo={bulkRegistrationInfo}
      />

      {selectedPlayers.length > 0 && (
        <Box
          sx={{
            mt: 2,
            mb: 2,
            gap: 2,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Button
            variant="contained"
            sx={{
              fontSize: 16,
              bgcolor: "#3f51b5",
              "&:hover": {
                bgcolor: "#303f9f",
              },
            }}
            onClick={handleSubmitList}
            startIcon={
              <Box
                sx={{
                  bgcolor: "white",
                  color: "#3f51b5",
                  borderRadius: "50%",
                  width: 24,
                  height: 24,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontWeight: "bold",
                }}
              >
                {selectedPlayers.length}
              </Box>
            }
          >
            {bulkRegistrationInfo &&
            bulkRegistrationInfo.registrationStatus === "pending"
              ? `Update Pending Registration #${bulkRegistrationInfo.bulkRegistrationId}`
              : "Create New Bulk Registration"}
          </Button>
        </Box>
      )}
    </Container>
  );
};

export default BulkRegistrationPage;
