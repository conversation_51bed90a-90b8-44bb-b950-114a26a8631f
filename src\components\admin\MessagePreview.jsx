import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Divider,
  Paper,
  Chip,
  IconButton,
} from "@mui/material";
import { Close, Email, Sms, WhatsApp } from "@mui/icons-material";

const MessagePreview = ({ 
  open, 
  onClose, 
  subject, 
  content, 
  type, 
  recipients = [] 
}) => {
  const getIcon = () => {
    switch (type) {
      case "email":
        return <Email color="primary" />;
      case "sms":
        return <Sms color="primary" />;
      case "whatsapp":
        return <WhatsApp color="success" />;
      default:
        return <Email color="primary" />;
    }
  };

  const getTitle = () => {
    const typeLabel = type.charAt(0).toUpperCase() + type.slice(1);
    return `${typeLabel} Preview`;
  };

  const formatContent = (content) => {
    if (type === "email") {
      // For email, render HTML content
      return <div dangerouslySetInnerHTML={{ __html: content }} />;
    } else {
      // For SMS/WhatsApp, render as plain text with line breaks
      return (
        <Typography variant="body1" sx={{ whiteSpace: "pre-wrap" }}>
          {content}
        </Typography>
      );
    }
  };

  const getCharacterCount = () => {
    // Remove HTML tags for character count
    const plainText = content.replace(/<[^>]*>/g, '');
    return plainText.length;
  };

  const getSMSCount = () => {
    const plainText = content.replace(/<[^>]*>/g, '');
    return Math.ceil(plainText.length / 160);
  };

  const getRecipientSummary = () => {
    if (recipients.length === 0) return "No recipients";
    if (recipients.length === 1) return `1 recipient`;
    return `${recipients.length} recipients`;
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: "60vh" }
      }}
    >
      <DialogTitle sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {getIcon()}
        {getTitle()}
        <Box sx={{ flexGrow: 1 }} />
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {/* Message Info */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: "#f5f5f5" }}>
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 2, mb: 2 }}>
            <Chip 
              label={getRecipientSummary()} 
              color="primary" 
              variant="outlined" 
            />
            <Chip 
              label={`${getCharacterCount()} characters`} 
              variant="outlined" 
            />
            {type === "sms" && (
              <Chip 
                label={`${getSMSCount()} SMS${getSMSCount() > 1 ? ' messages' : ''}`} 
                variant="outlined"
                color={getSMSCount() > 1 ? "warning" : "default"}
              />
            )}
          </Box>
          
          {recipients.length > 0 && (
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Recipients:
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {recipients.slice(0, 5).map((recipient, index) => (
                  <Chip
                    key={index}
                    label={recipient.name}
                    size="small"
                    variant="outlined"
                  />
                ))}
                {recipients.length > 5 && (
                  <Chip
                    label={`+${recipients.length - 5} more`}
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                )}
              </Box>
            </Box>
          )}
        </Paper>

        {/* Message Content */}
        <Paper sx={{ p: 3, border: "1px solid #e0e0e0" }}>
          {/* Subject (for email only) */}
          {type === "email" && subject && (
            <>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Subject:
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: "normal" }}>
                  {subject}
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
            </>
          )}

          {/* Message Body */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Message:
            </Typography>
            <Box sx={{ 
              p: 2, 
              bgcolor: type === "email" ? "white" : "#f9f9f9",
              border: "1px solid #e0e0e0",
              borderRadius: 1,
              minHeight: 200,
              maxHeight: 400,
              overflow: "auto"
            }}>
              {formatContent(content)}
            </Box>
          </Box>

          {/* Warnings for SMS */}
          {type === "sms" && getSMSCount() > 1 && (
            <Box sx={{ mt: 2, p: 2, bgcolor: "#fff3cd", borderRadius: 1 }}>
              <Typography variant="body2" color="warning.dark">
                ⚠️ This message will be sent as {getSMSCount()} SMS messages due to length.
              </Typography>
            </Box>
          )}

          {/* Character limit warning */}
          {type === "sms" && getCharacterCount() > 1600 && (
            <Box sx={{ mt: 2, p: 2, bgcolor: "#f8d7da", borderRadius: 1 }}>
              <Typography variant="body2" color="error.dark">
                ⚠️ Message is very long ({getCharacterCount()} characters). Consider shortening it.
              </Typography>
            </Box>
          )}
        </Paper>

        {/* Sample Recipients Preview */}
        {recipients.length > 0 && (
          <Paper sx={{ p: 2, mt: 3, bgcolor: "#f0f7ff" }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Sample recipient preview:
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              {recipients.slice(0, 3).map((recipient, index) => (
                <Box key={index} sx={{ display: "flex", gap: 2 }}>
                  <Typography variant="body2" sx={{ fontWeight: "medium", minWidth: 100 }}>
                    {recipient.name}:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {type === "email" ? recipient.email : recipient.phone || "No phone"}
                  </Typography>
                </Box>
              ))}
              {recipients.length > 3 && (
                <Typography variant="body2" color="text.secondary">
                  ... and {recipients.length - 3} more recipients
                </Typography>
              )}
            </Box>
          </Paper>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Close Preview
        </Button>
        <Button onClick={onClose} variant="contained" color="primary">
          Looks Good
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MessagePreview;
