import React, { useState, useEffect, useCallback } from "react";
import { Typography, Autocomplete, TextField, Fade, CircularProgress, Box } from "@mui/material";
import { Controller, useController } from "react-hook-form";
import { Client } from "../../api/client";

const ClubAutocomplete = ({
    name,
    control,
    placeholder = "Search for an Clubs",
    title = "Clubs",
    sx = {},
    required = false,
    disabled = false,
    rules = {},
    fetchUrl = "/club/get",
    defaultValue = null,
    setValue
}) => {
    const [clubs, setClubs] = useState([]);
    const [loading, setLoading] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [open, setOpen] = useState(false);
    const [isOthersSelected, setIsOthersSelected] = useState(false);
    const [customClubName, setCustomClubName] = useState("");
    


    // Memoize the fetch function to prevent recreation on each render
    const fetchClubs = useCallback(async (searchTerm) => {
        if (!searchTerm || searchTerm.trim() === "") {
            setClubs([]);
            return;
        }

        setLoading(true);
        try {
            const response = await Client.get(`${fetchUrl}/${encodeURIComponent(searchTerm)}`);
            setClubs([...(response.data.data || []), { id: "others", clubId: 'others', clubName: "Others" }]);
        } catch (error) {
            console.error("Error fetching clubs:", error);
            setClubs([]);
        } finally {
            setLoading(false);
        }
    }, [fetchUrl]);

    // Use debouncing to limit API calls
    useEffect(() => {
        const debounceTimer = setTimeout(() => {
            if (inputValue.trim()) {
                fetchClubs(inputValue);
            } else {
                setClubs([]);
            }
        }, 500);

        return () => clearTimeout(debounceTimer);
    }, [inputValue, fetchClubs]);

    return (
        <>
            <Typography
                variant="h6"
                sx={{
                    textAlign: "start",
                    p: "0px !important",
                    m: "0px !important",
                }}
            >
                {title}
                {required && <span style={{ color: "red" }}>*</span>}
            </Typography>
            <Controller
                name={name}
                control={control}
                rules={rules}
                defaultValue={defaultValue}
                render={({
                    field: { onChange, value, ref, ...field },
                    fieldState: { error },
                }) => (
                    <Autocomplete
                        options={clubs}
                        getOptionLabel={(option) => option?.clubName || ""}
                        value={value || null}
                        sx={{
                            "& .MuiInputBase-root": { p: "0px 5px !important" },
                            color: "black",
                            ...sx,
                        }}
                        onChange={(_, newValue) => {
                            if (newValue?.id === "others") {
                                setIsOthersSelected(true);
                                setValue(name, { id: "others", clubName: "" });
                                onChange({ id: "others", clubName: "" });
                            } else {
                                setIsOthersSelected(false);
                                setCustomClubName("");
                                onChange(newValue || { name: null, id: null });
                            }
                        }}
                        onInputChange={(_, newInputValue) => {
                            setInputValue(newInputValue);
                        }}
                        open={open && clubs.length > 0}
                        onOpen={() => setOpen(true)}
                        onClose={() => setOpen(false)}
                        isOptionEqualToValue={(option, value) =>
                            option?.id === value?.id
                        }
                        loading={loading}
                        disabled={disabled}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                {...field}
                                inputRef={ref}
                                placeholder={placeholder}
                                variant="outlined"
                                margin="normal"
                                error={!!error}
                                InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                        <>
                                            {loading ? <CircularProgress color="inherit" size={20} /> : null}
                                            {params.InputProps.endAdornment}
                                        </>
                                    ),
                                }}
                                helperText={
                                    error ? (
                                        <Fade in={!!error}>
                                            <Typography
                                                component="span"
                                                variant="caption"
                                                color="error"
                                            >
                                                {error?.message}
                                            </Typography>
                                        </Fade>
                                    ) : null
                                }
                            />
                        )}
                        renderOption={(props, option) => (
                            <li {...props} key={option.id}>
                                {option.clubName}

                            </li>
                        )}
                    />
                )}
            />
            {isOthersSelected && (
                <>
                <Typography sx={{textAlign:'start'}} variant="body1" >Enter your club name</Typography>
                <TextField
                    fullWidth
                    placeholder="Enter your Custom Club Name"
                    value={customClubName}
                    onChange={(e) => {
                        const newValue = e.target.value;
                        setCustomClubName(newValue);
                        setValue('club',  e.target.value );
                        setValue('club_others',true);
                    }}
                    variant="outlined"
                    margin="normal"
                />
                </>
            )}
        </>
    );
};

export default ClubAutocomplete;