import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Grid,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
} from "@mui/material";
import { Search as SearchIcon, RestartAlt } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const UserSearchForm = ({ selectedType, onSearchResults, loading, setLoading }) => {
  const toast = UseToast();
  
  const [search, setSearch] = useState({
    name: "",
    id: "",
    email: "",
    phone: "",
    country: "",
    state: "",
    district: "",
    city: "",
  });

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [cities, setCities] = useState([]);

  // Reset search when type changes
  useEffect(() => {
    handleReset();
  }, [selectedType]);

  // Load countries on mount
  useEffect(() => {
    fetchCountries();
  }, []);

  const fetchCountries = async () => {
    try {
      const response = await Client.get("/location/countries");
      if (response.data.success) {
        setCountries(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching countries:", error);
    }
  };

  const fetchStates = async (country) => {
    try {
      const response = await Client.get(`/location/states?country=${country}`);
      if (response.data.success) {
        setStates(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching states:", error);
    }
  };

  const fetchDistricts = async (state) => {
    try {
      const response = await Client.get(`/location/districts?state=${state}`);
      if (response.data.success) {
        setDistricts(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
    }
  };

  const fetchCities = async (district) => {
    try {
      const response = await Client.get(`/location/cities?district=${district}`);
      if (response.data.success) {
        setCities(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  };

  const handleInputChange = (field, value) => {
    setSearch(prev => ({ ...prev, [field]: value }));

    // Handle cascading location dropdowns
    if (field === "country") {
      setSearch(prev => ({ ...prev, state: "", district: "", city: "" }));
      setStates([]);
      setDistricts([]);
      setCities([]);
      if (value) fetchStates(value);
    } else if (field === "state") {
      setSearch(prev => ({ ...prev, district: "", city: "" }));
      setDistricts([]);
      setCities([]);
      if (value) fetchDistricts(value);
    } else if (field === "district") {
      setSearch(prev => ({ ...prev, city: "" }));
      setCities([]);
      if (value) fetchCities(value);
    }
  };

  const handleReset = () => {
    setSearch({
      name: "",
      id: "",
      email: "",
      phone: "",
      country: "",
      state: "",
      district: "",
      city: "",
    });
    setStates([]);
    setDistricts([]);
    setCities([]);
  };

  const handleSearch = async () => {
    setLoading(true);
    try {
      let endpoint = "";
      let params = {};

      // Build endpoint and params based on selected type
      switch (selectedType) {
        case "player":
          endpoint = "/admin/players/search";
          params = {
            playerName: search.name,
            playerId: search.id,
            email: search.email,
            mobile: search.phone,
            country: search.country,
            state: search.state,
            district: search.district,
            city: search.city,
          };
          break;
        case "club":
          endpoint = "/admin/clubs/search";
          params = {
            clubName: search.name,
            clubId: search.id,
            email: search.email,
            country: search.country,
            state: search.state,
            district: search.district,
            city: search.city,
          };
          break;
        case "user":
          endpoint = "/admin/users/search";
          params = {
            name: search.name,
            userId: search.id,
            email: search.email,
            phone: search.phone,
            country: search.country,
            state: search.state,
            district: search.district,
            city: search.city,
          };
          break;
        case "tournament":
          endpoint = "/admin/tournaments/search";
          params = {
            tournamentName: search.name,
            tournamentId: search.id,
            country: search.country,
            state: search.state,
            district: search.district,
            city: search.city,
          };
          break;
        default:
          toast.error("Invalid search type");
          return;
      }

      const response = await Client.get(endpoint, { params });
      
      if (response.data.success) {
        onSearchResults(response.data.data || []);
        if (response.data.data?.length === 0) {
          toast.info("No results found");
        }
      } else {
        toast.error(response.data.message || "Search failed");
        onSearchResults([]);
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("Search failed. Please try again.");
      onSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const getSearchFields = () => {
    const baseFields = [
      { key: "name", label: getNameLabel(), type: "text" },
      { key: "id", label: getIdLabel(), type: "text" },
    ];

    if (selectedType !== "tournament") {
      baseFields.push(
        { key: "email", label: "Email", type: "text" },
        { key: "phone", label: "Phone", type: "text" }
      );
    }

    return baseFields;
  };

  const getNameLabel = () => {
    switch (selectedType) {
      case "player": return "Player Name";
      case "club": return "Club Name";
      case "tournament": return "Tournament Name";
      default: return "Name";
    }
  };

  const getIdLabel = () => {
    switch (selectedType) {
      case "player": return "Player ID/CBID";
      case "club": return "Club ID";
      case "tournament": return "Tournament ID";
      default: return "User ID";
    }
  };

  return (
    <Box>
      <Grid container spacing={2} alignItems="center">
        {/* Search Fields */}
        {getSearchFields().map((field) => (
          <Grid item xs={12} sm={6} md={3} key={field.key}>
            <TextField
              placeholder={field.label}
              variant="outlined"
              fullWidth
              size="small"
              value={search[field.key]}
              onChange={(e) => handleInputChange(field.key, e.target.value)}
              sx={{ bgcolor: "white" }}
            />
          </Grid>
        ))}

        {/* Location Fields */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Country</InputLabel>
            <Select
              value={search.country}
              label="Country"
              onChange={(e) => handleInputChange("country", e.target.value)}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="">All Countries</MenuItem>
              {countries.map((country) => (
                <MenuItem key={country} value={country}>
                  {country}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>State</InputLabel>
            <Select
              value={search.state}
              label="State"
              onChange={(e) => handleInputChange("state", e.target.value)}
              disabled={!search.country}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="">All States</MenuItem>
              {states.map((state) => (
                <MenuItem key={state} value={state}>
                  {state}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>District</InputLabel>
            <Select
              value={search.district}
              label="District"
              onChange={(e) => handleInputChange("district", e.target.value)}
              disabled={!search.state}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="">All Districts</MenuItem>
              {districts.map((district) => (
                <MenuItem key={district} value={district}>
                  {district}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>City</InputLabel>
            <Select
              value={search.city}
              label="City"
              onChange={(e) => handleInputChange("city", e.target.value)}
              disabled={!search.district}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="">All Cities</MenuItem>
              {cities.map((city) => (
                <MenuItem key={city} value={city}>
                  {city}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              disabled={loading}
              sx={{ minWidth: "40px" }}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearch}
              disabled={loading}
              startIcon={
                loading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <SearchIcon />
                )
              }
              fullWidth
            >
              Search
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserSearchForm;
