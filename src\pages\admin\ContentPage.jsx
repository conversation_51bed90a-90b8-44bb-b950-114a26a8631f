import {
  Box,
  Button,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Typography,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import RichTextEditor from "../../components/admin/RichTextEditor";
import BackButton from "../../components/common/BackButton";
import { Client } from "../../api/client";
import CloseIcon from "@mui/icons-material/Close";
import UseToast from "../../lib/hooks/UseToast";



// Available page types
const PAGE_TYPES = [
  { value: "about-us", label: "About Us" },
  { value: "fair-play-policy", label: "Fair Play Policy" },
  { value: "privacy-policy", label: "Privacy Policy" },
  { value: "terms-and-conditions", label: "Terms & Conditions" },
  { value: "refund-policy", label: "Refund Policy" },
];

const ContentPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [content, setContent] = useState("");
  const [originalContent, setOriginalContent] = useState("");
  const [pageSlug, setPageSlug] = useState("");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const toast = UseToast();


  // Initialize page slug from URL or default to first option
  useEffect(() => {
    const pageFromUrl = searchParams.get("page");
    if (pageFromUrl && PAGE_TYPES.some((type) => type.value === pageFromUrl)) {
      setPageSlug(pageFromUrl);
    } else if (PAGE_TYPES.length > 0) {
      setPageSlug(PAGE_TYPES[0].value);
      setSearchParams({ page: PAGE_TYPES[0].value });
    }
  }, [searchParams, setSearchParams]);

  // Fetch content from backend - memoized with useCallback
  const fetchContent = useCallback(
    async (slug) => {
      setLoading(true);
      setError(null);
      try {
        const response = await Client.get(`/admin/content/${slug}`);
        if(response.status === 204) {
          setContent("");
          setOriginalContent("");
          setError("Content not found");
          return;
        }
        if (response.data.success) {
          const contentData = response.data.data || "";
          setContent(contentData);
          setOriginalContent(contentData);
        } else {
          setError(response.data?.error || "Failed to fetch content");
          toast.error(response.data.error || "Failed to fetch content");
        }
      } catch (error) {
        console.error("Error fetching content:", error);
        setError("Error fetching content. Please try again.");
        toast.error("Error fetching content. Please try again.");
      } finally {
        setLoading(false);
      }
    },
    [toast]
  );
  // Fetch content when page slug changes
  useEffect(() => {
    if (pageSlug) {
      fetchContent(pageSlug);
    }
  }, [pageSlug]);

  // Handle content change
  const handleContentChange = (newContent) => {
    setContent(newContent);
  };

  // Handle page type change
  const handlePageChange = (event) => {
    const newPageSlug = event.target.value;
    setPageSlug(newPageSlug);
    setSearchParams({ page: newPageSlug });
  };

  // Handle save
  const handleSave = async () => {
    setSaving(true);
    try {
      console.log("Saving content:", content);
      if (!content) return;

      if (originalContent === "") {
        const response = await Client.post(`/admin/content`, {
          slug: pageSlug,
          htmlContent: content,
        });

        if (response.data.success) {
          setOriginalContent(content);
          toast.success("Content saved successfully");
        } else {
          toast.error(response.data.message || "Failed to save content");
        }
        return;
      } else if (originalContent !== content) {
        const response = await Client.put(`/admin/content/${pageSlug}`, {
          htmlContent: content,
        });

        if (response.data.success) {
          setOriginalContent(content);
          toast.success("Content saved successfully");
        } else {
          toast.error(response.data.message || "Failed to save content");
        }
      }
    } catch (error) {
      console.error("Error saving content:", error);
      toast.error("Error saving content. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setContent(originalContent);
    toast.info("Content reset to last saved version");
  };

  // Toggle preview dialog
  const togglePreview = () => {
    setPreviewOpen(!previewOpen);
  };

  // Get current page title
  const getCurrentPageTitle = () => {
    const page = PAGE_TYPES.find((type) => type.value === pageSlug);
    return page ? page.label : "Content";
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      <Box>
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3 }}>
          <Typography variant="h3" component="h1" gutterBottom align="start">
            Content Management
          </Typography>
          <Box>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              sx={{ mr: 1 }}
              disabled={loading || saving || content === originalContent}
            >
              Reset
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={togglePreview}
              sx={{ mr: 1 }}
              disabled={loading || saving}
            >
              Preview
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSave}
              disabled={loading || saving || content === originalContent}
            >
              {saving ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                "Save Content"
              )}
            </Button>
          </Box>
        </Box>

        {/* Page selector */}
        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel id="page-select-label" sx={{ color: "black" }}>
            Page
          </InputLabel>
          <Select
            labelId="page-select-label"
            id="page-select"
            value={pageSlug}
            label="Page"
            onChange={handlePageChange}
            disabled={loading || saving}
          >
            {PAGE_TYPES.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                {type.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {error && (
          <Alert severity="error" sx={{ mb: 3,fontSize:14 }}>
            {error}
          </Alert>
        )}

        {/* Editor */}
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <RichTextEditor
              value={content}
              onChange={handleContentChange}
              placeholder="Write your content here..."
              label={`Editing: ${getCurrentPageTitle()}`}
              minHeight={400}
              readOnly={saving}
            />
          )}
        </Paper>

        {/* Preview Dialog */}
        <Dialog
          open={previewOpen}
          onClose={togglePreview}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="h6">
              {getCurrentPageTitle()} - Preview
            </Typography>
            <IconButton
              edge="end"
              color="inherit"
              onClick={togglePreview}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Box
              sx={{
                p: 2,
                border: "1px solid #e0e0e0",
                borderRadius: 1,
                backgroundColor: "#fff",
                minHeight: "300px",
                maxHeight: "70vh",
                overflow: "auto",
              }}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={togglePreview}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default ContentPage;
