import axios from "axios";

const backend_baseURL = import.meta.env.VITE_API_URL;

export const Client = axios.create({
  baseURL: backend_baseURL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Token management without hooks
let currentToken = localStorage.getItem("jwt_token") || null;
let refreshAttempts = 0;
const MAX_REFRESH_ATTEMPTS = 3;
let isRefreshing = false;

// Initialize token from localStorage on module load
if (currentToken) {
  Client.defaults.headers.common["Authorization"] = `Bearer ${currentToken}`;
}

export const updateToken = (token) => {
  currentToken = token;
  if (token) {
    localStorage.setItem("jwt_token", token);
    Client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    localStorage.removeItem("jwt_token");
    delete Client.defaults.headers.common["Authorization"];
  }
};

// UPDATED: Refresh token function with proper 403 handling
export const refreshToken = async () => {
  // Prevent multiple concurrent refresh attempts
  if (isRefreshing) {
    return null;
  }

  isRefreshing = true;

  try {
    const response = await Client.get("/auth/refresh", {
      withCredentials: true,
    });

    if (!response.data.success) {
      throw new Error("Refresh token failed");
    }

    const { accessToken } = response.data.data;
    refreshAttempts = 0; // Reset attempts on success
    return accessToken;
  } catch (error) {
    console.error("Error refreshing token:", error);

    // Handle 403 - refresh token expired/invalid
    if (error.response?.status === 403) {
      console.log("Refresh token expired, logging out...");

      // Clear token immediately in API client
      updateToken(null);

      // Dispatch logout event - your GlobalContext will handle ALL the cleanup
      window.dispatchEvent(
        new CustomEvent("logout", {
          detail: { reason: "Session expired" },
        })
      );

      return null;
    }

    return null;
  } finally {
    isRefreshing = false;
  }
};

// Set up request interceptor
Client.interceptors.request.use((config) => {
  // Always check localStorage to ensure we have the latest token
  // This is important for the initial page load and cross-tab communication
  const token = localStorage.getItem("jwt_token") || currentToken;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    // Keep currentToken in sync
    if (token !== currentToken) {
      currentToken = token;
    }
  }
  return config;
});

// Set up response interceptor
Client.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (originalRequest.url?.includes("/auth/refresh")) {
      return Promise.reject(error);
    }
    if (
      (error.response &&
        error.response.status === 401 &&
        error.response.data?.message === "Token has expired") ||
      (error.response.data?.message ===
        "No authentication token, access denied" &&
        !originalRequest._retry &&
        refreshAttempts < MAX_REFRESH_ATTEMPTS)
    ) {
      originalRequest._retry = true;
      refreshAttempts++;

      try {
        const newToken = await refreshToken();
        if (newToken) {
          updateToken(newToken);
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
          }
          return Client(originalRequest);
        }
      } catch (refreshError) {
        updateToken(null);
        return Promise.reject(refreshError);
      }
    } else if (
      (error.response &&
        error.response.status === 401 &&
        error.response.data?.message ===
          "No authentication token, access denied") ||
      error.response.data?.message === "Invalid token"
    ) {
      // Instead of calling logoutUser directly, we'll dispatch an event
      // Dispatch a logout event
      window.dispatchEvent(new CustomEvent("logout"));

      updateToken(null);
      localStorage.removeItem("app_config");

      // Don't force page reload - let the React Router handle navigation
      // This prevents the 404 page flash during auth refresh
      // window.location.reload();

      return Promise.reject(error);
    }

    if (refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
      refreshAttempts = 0;
      updateToken(null);
      window.dispatchEvent(
        new CustomEvent("logout", {
          detail: { reason: "Authentication failed" },
        })
      );
    }

    return Promise.reject(error);
  }
);
