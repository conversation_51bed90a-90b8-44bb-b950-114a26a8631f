import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Divider,
  Alert,
  Link,
  TableContainer,
  Table,
  TableBody,
  TableCell,
  TableRow
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ReceiptIcon from '@mui/icons-material/Receipt';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import DownloadIcon from '@mui/icons-material/Download';
import { Client } from '../api/client';

const PaymentSuccessPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [paymentInfo, setPaymentInfo] = useState({
    txnId: '',
    amount: '',
    tournamentName: '',
    date: new Date().toLocaleDateString()
  });

  useEffect(() => {
    // Hard coded
    // Parse URL parameters
    const search = location.search;

    // Extract transaction ID if available
    const txnidMatch = search.match(/txnid=([^&?]+)/);
    const txnid = txnidMatch ? txnidMatch[1] : '5689896265';

    // Extract amount if available
    const amountMatch = search.match(/amount=([^&?]+)/);
    const amount = amountMatch ? decodeURIComponent(amountMatch[1]) : '1500';

    // Extract tournament name if available
    const tournamentMatch = search.match(/tournament=([^&?]+)/);
    const tournament = tournamentMatch ? decodeURIComponent(tournamentMatch[1]) : 'chennai-chess-championship';

    setPaymentInfo({
      txnId: txnid,
      amount: amount,
      tournamentName: tournament,
      date: new Date().toLocaleDateString()
    });
  }, [location.search]);
  
const HandleReceipt = async () => {
  try {
    // console.log("Requesting receipt for transaction:", paymentInfo.txnId);
    
    // Show loading indicator if you have one
    // setLoading(true);
    
    // Create the URL with proper encoding
    const encodedTxnId = encodeURIComponent(paymentInfo.txnId);
    const url = `/report/payment-receipt?txnId=${encodedTxnId}`;
    
    // console.log("Making request to:", url);
    
    // Make the API request with explicit responseType
    const response = await Client.get(url, {
      responseType: 'blob',  // Important: Use blob for binary data
      headers: {
        'Accept': 'application/pdf',
      }
    });
    
    // console.log("Response received:", {
    //   status: response.status,
    //   contentType: response.headers['content-type'],
    //   size: response.data?.size
    // });
    
    // Validate the response
    if (!response.data || response.data.size === 0) {
      console.error("Empty response received");
      alert("Failed to download PDF: Empty response received");
      return;
    }
    
    // Validate content type
    const contentType = response.headers['content-type'];
    if (contentType && !contentType.includes('application/pdf')) {
      console.warn("Received non-PDF content type:", contentType);
      
      // If we received something other than a PDF, handle it
      if (contentType.includes('application/json')) {
        // Try to read as JSON error message
        const reader = new FileReader();
        reader.onload = function() {
          try {
            const jsonData = JSON.parse(reader.result);
            console.error("Server returned error:", jsonData);
            alert(`Error: ${jsonData.error || jsonData.message || 'Unknown error'}`);
          } catch (e) {
            console.error("Failed to parse error response:", e);
            alert("Failed to download PDF: Server returned an invalid response");
          }
        };
        reader.readAsText(response.data);
        return;
      }
    }
    
    // Create URL from blob
    const blobUrl = window.URL.createObjectURL(response.data);
    
    // console.log("PDF blob URL created:", blobUrl);
    
    // For debugging - open in new tab to see if it renders properly
    // window.open(blobUrl, '_blank');
    
    // Create download link
    const a = document.createElement("a");
    a.href = blobUrl;
    a.download = `payment_receipt_${paymentInfo.txnId}.pdf`;
    a.style.display = 'none';
    document.body.appendChild(a);
    
    // console.log("Triggering download");
    a.click();
    
    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(blobUrl);
      // console.log("Download triggered and resources cleaned up");
    }, 100);
    
  } catch (error) {
    console.error("Download failed:", error);
    
    let errorMessage = "Failed to download receipt";
    
    if (error.response) {
      console.error("Error response details:", {
        status: error.response.status,
        data: error.response.data
      });
      
      errorMessage += ` (Status: ${error.response.status})`;
    } else if (error.request) {
      console.error("No response received from server");
      errorMessage += " - No response from server";
    } else {
      console.error("Error detail:", error.message);
      errorMessage += `: ${error.message}`;
    }
    
    alert(errorMessage);
  } finally {
    // Hide loading indicator if you have one
    // setLoading(false);
  }
};

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderRadius: 2,
          border: '1px solid #e0e0e0',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <CheckCircleOutlineIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
          <Typography variant="h4" component="h1" color="success.main">
            Registration Successful
          </Typography>
        </Box>

        <Alert severity="success" sx={{ mb: 3, fontSize: 16 }}>
          Thank you! Your payment has been successfully processed and your tournament registration is confirmed.
        </Alert>

        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Payment Details
          </Typography>

          <TableContainer component={Paper} sx={{ bgcolor: 'grey.50' }}>
            <Table >
              <TableBody>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Transaction ID</TableCell>
                  <TableCell>{paymentInfo.txnId || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                  <TableCell>{paymentInfo.date || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Amount Paid</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>{paymentInfo.amount || 'N/A'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Tournament</TableCell>
                  <TableCell>{paymentInfo.tournamentName || 'N/A'}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Typography variant="h6" gutterBottom>
          What's Next?
        </Typography>

        <Box component="ul" sx={{ pl: 2 }}>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            Your registration for the tournament is confirmed
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            A confirmation email has been sent to your registered email address
          </Typography>
          <Typography component="li" variant="body1" sx={{ mb: 1 }}>
            You can view tournament details and your registration in your dashboard
          </Typography>
          <Typography component="li" variant="body1">
            If you have any questions, feel free to contact our support team
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ flex: 1, fontSize: 16 }}
          >
            Go to Dashboard
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<EmojiEventsIcon />}
            onClick={() => navigate('/tournaments')}
            sx={{
              flex: 1,
              fontSize: 16,
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              }
            }}
          >
            View Tournaments
          </Button>
        </Box>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<DownloadIcon />}
            onClick={() =>HandleReceipt()}
            sx={{ fontSize: 16 }}
          >
            Download Receipt
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentSuccessPage;