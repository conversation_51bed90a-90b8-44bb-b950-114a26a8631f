import React, { useState } from 'react';
import { Button } from '@mui/material';
import EnquiryModal from './EnquiryModal';

const EnquireButton = ({ 
  variant = 'contained', 
  color = 'primary', 
  size = 'medium',
  sx = {},
  fullWidth = false,
  email
}) => {
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        variant={variant}
        color={color}
        size={size}
        onClick={handleOpen}
        sx={{
          borderRadius: '4px',
          textTransform: 'none',
          fontWeight: 'bold',
          ...sx
        }}
        fullWidth={fullWidth}
      >
        Enquire now
      </Button>
      
      <EnquiryModal email={email?.contactPersonEmail} open={open} onClose={handleClose} />
    </>
  );
};

export default EnquireButton;
