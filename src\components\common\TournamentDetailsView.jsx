import React, { useEffect, useState } from "react";
import { DetailTable } from "./DetailTable";

import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  capitalizeFirstLetter,
  formatAgeCategory,
  formatDateToDMY,
  formatDecimal,
} from "../../utils/formatters";
import UseGobalContext from "../../lib/hooks/UseGlobalContext";

const TournamentDetailsView = ({ tournaments }) => {
  const [expanded, setExpanded] = useState({
    tournamentDetails: true,
    personalInfo: true,
    venueDetails: true,
    prizeDetails: true,
    miscDetails: true,
  });
  const { user } = UseGobalContext();
  const [tournamentData, setTournamentData] = useState({});
  useEffect(() => {
    if (!tournaments) return;
    setTournamentData(formatTournamentData(tournaments));
  }, [tournaments]);

  const handleAccordionChange = (panel) => (_, isExpanded) => {
    setExpanded({
      ...expanded,
      [panel]: isExpanded,
    });
  };
  const color =
    user?.role !== "club"
      ? { odd: "#F5F2FC", even: "#F2FDF9" }
      : { odd: "#F3BEB90D", even: "#F3BEB926" };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        overflow: "hidden",
        mb: 4,
        backgroundColor: "#f5f9f6",
      }}
    >
      {tournamentData && (
        <TournamentHeader
          title={tournamentData.title || ""}
          subtitle={tournamentData.subtitle || ""}
          status={tournamentData.status || ""}
        />
      )}

      {tournamentData &&
        tournamentData.sections &&
        tournamentData.sections.map((section) => (
          <SectionAccordion
            key={section.id}
            title={section.title}
            expanded={expanded[section.id]}
            onChange={handleAccordionChange(section.id)}
          >
            <DetailTable details={section.details} rowColor={color} />
          </SectionAccordion>
        ))}
    </Paper>
  );
};

export default TournamentDetailsView;

// Format tournament data for display
function formatTournamentData(tournament) {
  if (!tournament) {
    return {
      title: "",
      subtitle: "",
      sections: [],
    };
  }

  return {
    title: tournament.title
      ? tournament.title.toUpperCase().replace(/-/g, " ")
      : "",
    subtitle: `${
      tournament.nationalApproval
        ? `National Approval ${tournament.nationalApproval}${
            tournament.stateApproval || tournament.districtApproval ? " | " : ""
          }`
        : ""
    }${
      tournament.stateApproval
        ? `State Approval ${tournament.stateApproval}${
            tournament.districtApproval ? " | " : ""
          }`
        : ""
    }${
      tournament.districtApproval
        ? `District Approval ${tournament.districtApproval}`
        : ""
    }`,
    status: (() => {
      try {
        const now = new Date();
        if (
          !tournament.registrationStartDate ||
          !tournament.registrationEndDate ||
          !tournament.startDate ||
          !tournament.endDate
        ) {
          return { name: "Unknown", color: "#757575" };
        }

        if (new Date(tournament.registrationStartDate) > now) {
          return { name: "Upcoming", color: "#4CAF50" };
        } else if (
          new Date(tournament.registrationStartDate) <= now &&
          new Date(tournament.registrationEndDate) > now
        ) {
          return { name: "Registration Open", color: "#2196F3" };
        } else if (
          new Date(tournament.registrationEndDate) <= now &&
          new Date(tournament.startDate) > now
        ) {
          return { name: "Registration Closed", color: "#F44336" };
        } else if (
          new Date(tournament.startDate) <= now &&
          new Date(tournament.endDate) > now
        ) {
          return { name: "Ongoing", color: "#FF9800" };
        } else {
          return { name: "Completed", color: "#757575" };
        }
      } catch (error) {
        console.error("Error determining tournament status:", error);
        return { name: "Unknown", color: "#757575" };
      }
    })(),
    sections: [
      {
        id: "tournamentDetails",
        title: "Tournament Details:",
        details: [
          {
            label: "Organizer Name",
            value: capitalizeFirstLetter(tournament.organizerName) || "-",
          },
          {
            label: "Tournament Level",
            value: capitalizeFirstLetter(tournament.tournamentLevel) || "-",
          },
          {
            label: "FIDE Rated",
            value: `${tournament.fideRated ? "Rated" : "Unrated"}`,
          },
          {
            label: "Tournament Date & Time",
            value:
              tournament.startDate && tournament.endDate
                ? `${formatDateToDMY(
                    tournament.startDate
                  )} to ${formatDateToDMY(tournament.endDate)} at ${
                    tournament.reportingTime
                  }`
                : "Dates -",
          },
          {
            label: "Tournament Category",
            value: capitalizeFirstLetter(tournament.tournamentCategory) || "-",
          },

          {
            label: "Male Age Category",
            value: formatAgeCategory(tournament.maleAgeCategory) || "-",
          },
          {
            label: "Female Age Category",
            value: formatAgeCategory(tournament.femaleAgeCategory) || "-",
          },
          {
            label: "Registration Date & time",
            value:
              tournament.registrationStartDate &&
              tournament.registrationEndDate &&
              tournament.registrationEndTime
                ? `${formatDateToDMY(
                    tournament.registrationStartDate
                  )} to ${formatDateToDMY(tournament.registrationEndDate)} at ${
                    tournament.registrationEndTime
                  }`
                : "-",
          },

          {
            label: "Entry Fee",
            value: tournament.entryFee
              ? `${tournament.entryFeeCurrency} ${formatDecimal(
                  tournament.entryFee
                )}`
              : "-",
          },
          // { label: "Category", value: tournament.category },
          {
            label: "Time Control",
            value: tournament.timeControl
              ? `${capitalizeFirstLetter(tournament.timeControl)}${
                  tournament.timeControlDuration
                    ? ` - ${tournament.timeControlDuration}`
                    : ""
                }${
                  tournament.timeControlIncrement
                    ? ` + ${tournament.timeControlIncrement} Increment`
                    : ""
                }`
              : "-",
          },
          {
            label: "Tournament Type",
            value:
              tournament.tournamentType === "single"
                ? "Single Player"
                : tournament.tournamentType === "team"
                ? "Team"
                : "-",
          },
          {
            label: "No. of Rounds",
            value: tournament.numberOfRounds || "-",
          },
          {
            label: "Spot Entry",
            value: `${tournament.spotEntry ? "Yes" : "No"}`,
          },
        ],
      },
      {
        id: "personalInfo",
        title: "Personnel & Contact Info:",
        details: [
          {
            label: "Chief Arbiter Name",
            value: capitalizeFirstLetter(tournament?.arbiter?.name) || "-",
          },
          {
            label: "Tournament Director Name",
            value:
              capitalizeFirstLetter(tournament.tournamentDirectorName) || "-",
          },
          {
            label: "Contact Person Name",
            value: capitalizeFirstLetter(tournament.contactPersonName) || "-",
          },
          {
            label: "Contact Number",
            value: tournament.contactNumber || "-",
          },
          { label: "Email ID", value: tournament.email || "-" },
          {
            label: "Alternate Contact",
            value: tournament.alternateContactNumber || "-",
          },
        ],
      },
      {
        id: "venueDetails",
        title: "Tournament Venue Details:",
        details: [
          {
            label: "Venue Address",
            value: tournament.venueAddress || "-",
          },
          {
            label: "Nearest Landmark",
            value: tournament.nearestLandmark || "-",
          },
          { label: "City", value: tournament.city || "-" },
          {
            label: "District",
            value: capitalizeFirstLetter(tournament.district) || "-",
          },
          { label: "Pincode", value: tournament.pincode || "-" },
          { label: "State", value: tournament.state || "-" },
          { label: "Country", value: tournament.country || "-" },
        ],
      },
      {
        id: "prizeDetails",
        title: "Tournament Prize Details:",
        details: [
          {
            label: "Number of Trophies for Male",
            value: tournament.numberOfTrophiesMale || "-",
          },
          {
            label: "Number of Trophies for Female",
            value: tournament.numberOfTrophiesFemale || "-",
          },
          {
            label: "Total Cash Prize",
            value:
              tournament.totalCashPrizeCurrency &&
              tournament.totalCashPrizeAmount
                ? `${tournament.totalCashPrizeCurrency} ${formatDecimal(
                    tournament.totalCashPrizeAmount
                  )}`
                : "-",
          },
        ],
      },
      {
        id: "miscDetails",
        title: "Miscellaneous Details:",
        details: [
          {
            label: "Chess Board Provided",
            value: `${tournament.chessboardProvided == true ? "Yes" : "No"}`,
          },
          {
            label: "Timer Provided",
            value: `${tournament.timerProvided == true ? "Yes" : "No"}`,
          },
          {
            label: "Parking Facility",
            value: `${
              tournament.parkingFacility === "yes"
                ? "2 & 4 Wheeler"
                : tournament.parkingFacility === "limited"
                ? "2 Wheeler Only"
                : "Not Available"
            }`,
          },
          {
            label: "Canteen Facility",
            value: Array.isArray(tournament.foodFacility)
              ? tournament.foodFacility
                  .filter((value) => value !== "nil")
                  .join(", ") || "Not available"
              : "Not available",
          },
        ],
      },
    ],
  };
}

// Section Accordion Component
const SectionAccordion = ({ title, expanded, onChange, children }) => (
  <Accordion
    expanded={expanded}
    onChange={onChange}
    sx={{
      boxShadow: "none",
      "&:before": {
        display: "none",
        margin: "0 !important",
      },
      "&.MuiAccordion-root.Mui-expanded": {
        margin: "0 !important",
      },
    }}
  >
    <AccordionSummary
      expandIcon={<ExpandMoreIcon />}
      sx={{
        backgroundColor: "#e8f5e9",
        borderTop: "1px solid #c8e6c9",
        borderBottom: "1px solid #c8e6c9",
        minHeight: 56,
        "&.Mui-expanded": {
          minHeight: 56,
        },
      }}
    >
      <Typography
        sx={{
          fontWeight: "bold",
          color: "#000",
        }}
      >
        {title}
      </Typography>
    </AccordionSummary>
    <AccordionDetails sx={{ p: 0 }}>{children}</AccordionDetails>
  </Accordion>
);

// Tournament Header Component
const TournamentHeader = ({ title, subtitle, status }) => (
  <Box sx={{ p: 3, pb: 0, backgroundColor: "hsla(200, 89%, 89%, 1)" }}>
    <Typography
      variant="h2"
      component="h1"
      align="center"
      sx={{
        fontWeight: "bold",
        textWrap: "balance",
        fontSize: { xs: "1.5rem", sm: "2rem" },
      }}
    >
      {title}
    </Typography>
    <Typography
      variant="subtitle1"
      align="center"
      sx={{
        mt: 1,
        fontSize: { xs: "0.9rem", sm: "1.2rem" },
      }}
    >
      {subtitle}
    </Typography>
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        width: "100%",
        flexWrap: "wrap",
        justifyContent: "flex-end",
        gap: 2,
        p: 2,
      }}
    >
      <Typography>
        Status: <span style={{ color: status.color }}>{status.name}</span>
      </Typography>
    </Box>
  </Box>
);
