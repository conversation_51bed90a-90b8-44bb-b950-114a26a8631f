import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Chip,
  MenuItem,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import { RestartAlt } from "@mui/icons-material";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const PaymentHistoryPage = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);

  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    status: "all",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  // Handler functions
  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  const handleReset = () => {
    setSearch({
      transactionId: "",
      tournamentTitle: "",
      status: "all",
    });
    fetchPayments(1);
  };

  const handleSearch = () => {
    fetchPayments(1);
  };

  const fetchPayments = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Only add non-empty search parameters
        Object.keys(search).forEach((key) => {
          if (search[key] !== "") {
            params[key] = search[key];
          }
        });

        const response = await Client.get("/payment/user", { params });

        if (response.status === 204) {
          toast.info("No payment records found");
          setPayments([]);
          setTotalRecords(0);
          setTotalPages(0);
          setPage(1);
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        setPayments(response.data.data.payments);
        setTotalRecords(response.data.data.total);
        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search]
  );

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Get status chip color based on payment status
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
      case "success":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  // Initial fetch of payments
  useEffect(() => {
    fetchPayments(1);
  }, []);


     const HandleReceipt = async (txnId) => {
        try {
          const encodedTxnId = encodeURIComponent(txnId);
          const url = `/report/payment-receipt?txnId=${encodedTxnId}`;
          
          // console.log("Making request to:", url);
          
          // Make the API request with explicit responseType
          const response = await Client.get(url, {
            responseType: 'blob',  // Important: Use blob for binary data
            headers: {
              'Accept': 'application/pdf',
            }
          });
          
          // console.log("Response received:", {
          //   status: response.status,
          //   contentType: response.headers['content-type'],
          //   size: response.data?.size
          // });
          
          // Validate the response
          if (!response.data || response.data.size === 0) {
            console.error("Empty response received");
            alert("Failed to download PDF: Empty response received");
            return;
          }
          
          // Validate content type
          const contentType = response.headers['content-type'];
          if (contentType && !contentType.includes('application/pdf')) {
            console.warn("Received non-PDF content type:", contentType);
            
            // If we received something other than a PDF, handle it
            if (contentType.includes('application/json')) {
              // Try to read as JSON error message
              const reader = new FileReader();
              reader.onload = function() {
                try {
                  const jsonData = JSON.parse(reader.result);
                  console.error("Server returned error:", jsonData);
                  alert(`Error: ${jsonData.error || jsonData.message || 'Unknown error'}`);
                } catch (e) {
                  console.error("Failed to parse error response:", e);
                  alert("Failed to download PDF: Server returned an invalid response");
                }
              };
              reader.readAsText(response.data);
              return;
            }
          }
          
          // Create URL from blob
          const blobUrl = window.URL.createObjectURL(response.data);
          
          // console.log("PDF blob URL created:", blobUrl);
          
          // For debugging - open in new tab to see if it renders properly
          // window.open(blobUrl, '_blank');
          
          // Create download link
          const a = document.createElement("a");
          a.href = blobUrl;
          a.download = `payment_receipt_${txnId}.pdf`;
          a.style.display = 'none';
          document.body.appendChild(a);
          
          // console.log("Triggering download");
          a.click();
          
          // Clean up
          setTimeout(() => {
            document.body.removeChild(a);
            window.URL.revokeObjectURL(blobUrl);
            // console.log("Download triggered and resources cleaned up");
          }, 100);
          
        } catch (error) {
          console.error("Download failed:", error);
          
          let errorMessage = "Failed to download receipt";
          
          if (error.response) {
            console.error("Error response details:", {
              status: error.response.status,
              data: error.response.data
            });
            
            errorMessage += ` (Status: ${error.response.status})`;
          } else if (error.request) {
            console.error("No response received from server");
            errorMessage += " - No response from server";
          } else {
            console.error("Error detail:", error.message);
            errorMessage += `: ${error.message}`;
          }
          
          alert(errorMessage);
        } finally {
          // Hide loading indicator if you have one
          // setLoading(false);
        }
      };

  return (
    <Container
      maxWidth="xl"
      sx={{
        pt: 2,

        pb: 8,
        minHeight: "70dvh",
        
      }}
    >
      <BackButton/>
      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment History
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.transactionId}
              onChange={(e) =>
                setSearch({ ...search, transactionId: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter transaction ID"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.tournamentTitle}
              onChange={(e) =>
                setSearch({ ...search, tournamentTitle: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter tournament title"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              variant="outlined"
              fullWidth
              placeholder="Select status"
              value={search.status}
              onChange={(e) => setSearch({ ...search, status: e.target.value })}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="all" defaultChecked>
                All
              </MenuItem>
              <MenuItem value="paid">Paid</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="failed">Failed</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6} md={3} sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="containedSecondary"
              color="secondary"
              sx={{
                width: "40px",
              }}
              onClick={handleReset}
              disabled={loading}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Payment History Table */}
      <DynamicTable
        columns={[
          {
            id: "transactionId",
            label: "Transaction ID",
            format: (_, payment) => payment.paymentTransactionId || "N/A",
          },
          {
            id: "date",
            label: "Date",
            format: (_, payment) => formatDate(payment.paymentDate),
          },
          {
            id: "amount",
            label: "Amount",
            format: (_, payment) => (
              <Typography variant="body1" fontWeight="medium">
                {payment.paymentCurrency || "INR"}{" "}
                {payment.paymentAmount || "0.00"}
              </Typography>
            ),
          },
          {
            id: "paymentMethod",
            label: "Payment Method",
            format: (_, payment) => payment.paymentMode || "Online",
          },
          {
            id: "status",
            label: "Status",
            format: (_, payment) => (
              <Chip
                label={payment.paymentStatus || "Unknown"}
                color={getStatusColor(payment.paymentStatus)}
                size="small"
                variant="filled"
              />
            ),
          },
          {
            id: "tournament",
            label: "Tournament",
            format: (_, payment) =>
              payment?.tournament?.title ? (
                <Link
                  to={`/tournaments/${payment?.tournament?.title}`}
                  style={{ textDecoration: "none" }}
                >
                  <Typography
                    variant="body1"
                    color="primary"
                    sx={{
                      textTransform: "capitalize",
                      fontWeight: "medium",
                    }}
                  >
                    {payment?.tournament?.title
                      .toLowerCase()
                      .replace(/-/g, " ")}
                  </Typography>
                </Link>
              ) : (
                "N/A"
              ),
          },

          {
            id: "receipt",
            label: "Receipt",
            format: (_, payment) =>
              payment.paymentTransactionId && payment.paymentStatus === 'paid'? (
                  <Button variant="contained" size="small" onClick={()=>HandleReceipt(payment.paymentTransactionId)}>
                    Download Receipt
                  </Button>
              ) : (
                "N/A"
              ),
          },
        ]}
        data={payments}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath=""
        idField="id"
        showDetailsButton={false}

        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />

      {/* Record Count */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 2,
          p: 2,
          bgcolor: "#fafafa",
          borderRadius: 1,
        }}
      >
        <Typography variant="h6" color="text.primary">
          Showing {payments.length} of {totalRecords} records
        </Typography>
      </Box>
    </Container>
  );
};

export default PaymentHistoryPage;
