import React, { useState } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Paper,
  Button,
  Collapse,
  Chip,
  CircularProgress,
} from "@mui/material";
import { ExpandMore, ExpandLess, Clear } from "@mui/icons-material";

const TemplateSelector = ({ 
  templates, 
  selectedTemplate, 
  onTemplateSelect, 
  loading, 
  type = "email" 
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const [selectedTemplateData, setSelectedTemplateData] = useState(null);

  const handleTemplateChange = (templateId) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplateData(template);
    onTemplateSelect(template);
  };

  const handleClearTemplate = () => {
    setSelectedTemplateData(null);
    onTemplateSelect(null);
  };

  const getTemplatesByCategory = () => {
    const categorized = {};
    templates.forEach(template => {
      const category = template.category || "General";
      if (!categorized[category]) {
        categorized[category] = [];
      }
      categorized[category].push(template);
    });
    return categorized;
  };

  const renderTemplatePreview = (template) => {
    if (!template) return null;

    return (
      <Paper sx={{ p: 2, mt: 2, bgcolor: "#f9f9f9" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1 }}>
          <Typography variant="subtitle2" color="primary">
            Template Preview
          </Typography>
          <Button
            size="small"
            onClick={() => setShowPreview(!showPreview)}
            endIcon={showPreview ? <ExpandLess /> : <ExpandMore />}
          >
            {showPreview ? "Hide" : "Show"} Details
          </Button>
        </Box>
        
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Template: {template.name}
          </Typography>
          {template.category && (
            <Chip label={template.category} size="small" sx={{ mt: 0.5 }} />
          )}
        </Box>

        {type === "email" && template.subject && (
          <Box sx={{ mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Subject:
            </Typography>
            <Typography variant="body2">
              {template.subject}
            </Typography>
          </Box>
        )}

        <Collapse in={showPreview}>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Content Preview:
            </Typography>
            <Paper sx={{ p: 2, bgcolor: "white", maxHeight: 200, overflow: "auto" }}>
              {type === "email" ? (
                <div dangerouslySetInnerHTML={{ __html: template.content }} />
              ) : (
                <Typography variant="body2" sx={{ whiteSpace: "pre-wrap" }}>
                  {template.content}
                </Typography>
              )}
            </Paper>
          </Box>
        </Collapse>
      </Paper>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
        <CircularProgress size={20} />
        <Typography variant="body2">Loading templates...</Typography>
      </Box>
    );
  }

  const categorizedTemplates = getTemplatesByCategory();

  return (
    <Box>
      <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
        <FormControl fullWidth>
          <InputLabel>Select Template</InputLabel>
          <Select
            value={selectedTemplate || ""}
            label="Select Template"
            onChange={(e) => handleTemplateChange(e.target.value)}
          >
            <MenuItem value="">
              <em>No template (Custom content)</em>
            </MenuItem>
            
            {Object.entries(categorizedTemplates).map(([category, categoryTemplates]) => [
              <MenuItem key={`category-${category}`} disabled sx={{ fontWeight: "bold" }}>
                {category}
              </MenuItem>,
              ...categoryTemplates.map((template) => (
                <MenuItem key={template.id} value={template.id} sx={{ pl: 4 }}>
                  {template.name}
                  {template.description && (
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      - {template.description}
                    </Typography>
                  )}
                </MenuItem>
              ))
            ])}
          </Select>
        </FormControl>

        {selectedTemplate && (
          <Button
            variant="outlined"
            size="small"
            onClick={handleClearTemplate}
            startIcon={<Clear />}
          >
            Clear
          </Button>
        )}
      </Box>

      {selectedTemplateData && renderTemplatePreview(selectedTemplateData)}

      {templates.length === 0 && !loading && (
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          No templates available. You can create custom content below.
        </Typography>
      )}
    </Box>
  );
};

export default TemplateSelector;
