import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Divider,
  CircularProgress,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import RichTextEditor from "../../components/admin/RichTextEditor";
import TemplateSelector from "../../components/admin/TemplateSelector";
import MessagePreview from "../../components/admin/MessagePreview";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const EmailComposer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();
  
  const { selectedUsers = [], selectedType = "", mode = "email" } = location.state || {};
  
  const [emailData, setEmailData] = useState({
    subject: "",
    content: "",
    templateId: "",
    useTemplate: false,
  });
  
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (selectedUsers.length === 0) {
      toast.error("No recipients selected");
      navigate("/admin/dashboard/email");
      return;
    }
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/admin/email-templates");
      if (response.data.success) {
        setTemplates(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error("Failed to load email templates");
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    if (template) {
      setEmailData(prev => ({
        ...prev,
        subject: template.subject || "",
        content: template.content || "",
        templateId: template.id,
        useTemplate: true,
      }));
    } else {
      setEmailData(prev => ({
        ...prev,
        subject: "",
        content: "",
        templateId: "",
        useTemplate: false,
      }));
    }
  };

  const handleInputChange = (field, value) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value,
      useTemplate: field === "content" || field === "subject" ? false : prev.useTemplate,
    }));
  };

  const validateEmail = () => {
    if (!emailData.subject.trim()) {
      toast.error("Subject is required");
      return false;
    }
    if (!emailData.content.trim()) {
      toast.error("Email content is required");
      return false;
    }
    return true;
  };

  const handleSendEmail = async () => {
    if (!validateEmail()) return;

    setSending(true);
    try {
      const payload = {
        recipients: selectedUsers.map(user => ({
          id: user.id,
          email: user.email,
          name: user.name,
          type: user.type,
        })),
        subject: emailData.subject,
        content: emailData.content,
        templateId: emailData.useTemplate ? emailData.templateId : null,
        recipientType: selectedType,
      };

      const response = await Client.post("/admin/send-email", payload);
      
      if (response.data.success) {
        toast.success(`Email sent successfully to ${selectedUsers.length} recipients`);
        navigate("/admin/dashboard/email");
      } else {
        toast.error(response.data.message || "Failed to send email");
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email. Please try again.");
    } finally {
      setSending(false);
    }
  };

  const getRecipientSummary = () => {
    const typeLabel = selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
    return `${selectedUsers.length} ${typeLabel}${selectedUsers.length > 1 ? 's' : ''}`;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Compose Email
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Send email to selected recipients
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Composition Area */}
        <Grid item xs={12} md={8}>
          {/* Recipients Summary */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recipients ({getRecipientSummary()})
            </Typography>
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
              {selectedUsers.slice(0, 10).map((user, index) => (
                <Chip
                  key={index}
                  label={`${user.name} (${user.email})`}
                  variant="outlined"
                  size="small"
                />
              ))}
              {selectedUsers.length > 10 && (
                <Chip
                  label={`+${selectedUsers.length - 10} more`}
                  variant="outlined"
                  size="small"
                  color="primary"
                />
              )}
            </Box>
          </Paper>

          {/* Template Selection */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Email Template (Optional)
            </Typography>
            <TemplateSelector
              templates={templates}
              selectedTemplate={emailData.templateId}
              onTemplateSelect={handleTemplateSelect}
              loading={loading}
              type="email"
            />
          </Paper>

          {/* Email Composition */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Email Content
            </Typography>
            
            <TextField
              label="Subject"
              fullWidth
              value={emailData.subject}
              onChange={(e) => handleInputChange("subject", e.target.value)}
              sx={{ mb: 3 }}
              required
            />

            <RichTextEditor
              value={emailData.content}
              onChange={(content) => handleInputChange("content", content)}
              placeholder="Write your email content here..."
              label="Email Body"
              minHeight={300}
            />
          </Paper>

          {/* Action Buttons */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                onClick={() => setShowPreview(true)}
                disabled={!emailData.subject || !emailData.content}
              >
                Preview
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSendEmail}
                disabled={sending || !emailData.subject || !emailData.content}
                startIcon={sending && <CircularProgress size={20} />}
              >
                {sending ? "Sending..." : "Send Email"}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              Email Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Recipients
              </Typography>
              <Typography variant="body1">
                {getRecipientSummary()}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Subject
              </Typography>
              <Typography variant="body1">
                {emailData.subject || "No subject"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Template
              </Typography>
              <Typography variant="body1">
                {emailData.useTemplate ? "Using template" : "Custom content"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Content Length
              </Typography>
              <Typography variant="body1">
                {emailData.content.replace(/<[^>]*>/g, '').length} characters
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Preview Modal */}
      <MessagePreview
        open={showPreview}
        onClose={() => setShowPreview(false)}
        subject={emailData.subject}
        content={emailData.content}
        type="email"
        recipients={selectedUsers}
      />
    </Container>
  );
};

export default EmailComposer;
